package com.iflytek.edu.ai.demo

import android.R.attr.type
import android.app.Application
import android.content.Context
import android.util.ArrayMap
import com.v.skill_up_english.etingshuo.noOpDelegate
import com.iflytek.edu.ai.audio.common.EDUAIAudioAbilityBean
import com.iflytek.edu.ai.audio.common.EDUAIEngineError
import com.iflytek.edu.ai.audio.evaluate.EDUAIDataEvaluateManager
import com.iflytek.edu.ai.audio.evaluate.EDUAIEvaluateManager
import com.iflytek.edu.ai.audio.evaluate.bean.EDUAIEvaluateBean
import com.iflytek.edu.ai.audio.evaluate.bean.EDUAIEvaluateConfig
import com.iflytek.edu.ai.audio.evaluate.bean.EDUAIEvaluateRespBean
import com.iflytek.edu.ai.audio.evaluate.callback.IEDUAIEvaluateCallback
import com.iflytek.edu.ai.base.token.EDUAIGlobalConfig
import com.iflytek.edu.ai.global.EDUAIGlobalManager
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.collections.set
import kotlin.coroutines.resume
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlin.coroutines.resume

/**
 * @author: cursor
 * @date: 2025/6/27
 * @Description
 **/
object EvalManager {

    private   var isTest = false
    private val AI_WS_HOST =
        if (isTest) "wss://test-aiplat.changyan.com:8098" else "wss://ets.eduaiplat.com:8198"
    private val AI_HTTP_HOST =
        if (isTest) "http://ai-thor.ceshiservice.cn" else "https://ai-thor.eduaiplat.com"

    private enum class InitState { UNINITIALIZED, INITIALIZING, INITIALIZED, FAILED }
    private enum class EvalState { IDLE, RUNNING, FINISHED }

    @Volatile
    private var state = InitState.UNINITIALIZED
    @Volatile
    private var evalState = EvalState.IDLE
    private val mutex = Mutex()
    private val manager by lazy { EDUAIEvaluateManager() } // 评测管理类

    private fun generateConfigParams(token: String? = null, userId: String, appId: String, dvId: String,userTag: String,logEnable: Boolean = false, test: Boolean=false,extra: ArrayMap<String, Any>? = null): EDUAIGlobalConfig {
        isTest=test;
        val extraMap = extra?: ArrayMap<String, Any>()
        extraMap["aiWsHost"] = AI_WS_HOST
        return EDUAIGlobalConfig.builder()
            .setAppId(appId) //必填，appid
            .setAccessToken(token.orEmpty()) // 必填，用来鉴权的token
            .setDvId(dvId) // 必填，用来区分设备
            .setUserId(userId) // 必填，用来区分用户
            .setUserTag(userTag) // 必填，用来区分应用
            .setLogEnable(logEnable) //选填,是否打开日志
            .setHttpHost(AI_HTTP_HOST)
            .setExtra(extraMap)
            .build()
    }

    /** 初始化引擎：token建议自己加密缓存，然后保存到本地，在有效期内，使用token初始化，减少端上请求 */
    suspend fun init(context: Context, token:String, userId: String, appId: String, dvId: String,userTag: String,logEnable: Boolean = false, test: Boolean = false,extra: ArrayMap<String, Any>? = null): Int {
        mutex.withLock {
            when (state) {
                InitState.INITIALIZED -> return 1
                InitState.INITIALIZING -> return 0 // 正在初始化中，禁止重复调用
                InitState.FAILED, InitState.UNINITIALIZED -> {
                    state = InitState.INITIALIZING
                    try {
                        val config = generateConfigParams(token, userId, appId, dvId,userTag,logEnable,test,extra,)
                        // 初始化
                        return if (EDUAIGlobalManager.init(context, config)) {
                            println("init success")
                            state = InitState.INITIALIZED
                            1
                        } else {
                            println("init fail")
                            state = InitState.FAILED
                            0
                        }
                    } catch (e: Exception) {
                        state = InitState.FAILED
                        return 0
                    }
                }
            }
        }
    }


    suspend fun startEvaluate(
        evaluateBean:EDUAIEvaluateBean,
    ): EDUAIEvaluateRespBean = suspendCancellableCoroutine { continuation ->
        runCatching {
            val config = EDUAIEvaluateConfig().apply {
                debugMode = true // 调试开关
            }
            if (manager.init(config).not()) {
                continuation.resume(
                    createErrorRespBean(
                        "START_FAILED",
                        "Failed to init $type evaluation"
                    )
                )
                return@suspendCancellableCoroutine
            }

            evalState = EvalState.RUNNING
            // 结果回调listener
            val listener = object : IEDUAIEvaluateCallback by noOpDelegate() {
                override fun onResult(bean: EDUAIEvaluateRespBean) {
                    if (bean.responseFinish) {
                        evalState = EvalState.FINISHED
                        continuation.resume(bean)
                    }
                }
            }
            manager.start(evaluateBean, listener)
        }.onFailure { ex ->
            evalState = EvalState.FINISHED
            continuation.resume(
                createErrorRespBean(
                    "START_EXCEPTION",
                    ex.message ?: "Failed to start $type evaluation"
                )
            )
        }
    }

    private fun createErrorRespBean(code: String, msg: String) =
        EDUAIEvaluateRespBean().apply {
            engineError = EDUAIEngineError().apply {
                this.code = code
                this.msg = msg
            }
        }

    /** 是否正在评测 */
    fun isRunning() = manager.isRunning()

    /** ws是否连接成功 */
    fun isConnected() = manager.isConnected()

    /** 取消评测 */
    fun cancel() {
        if (evalState == EvalState.RUNNING) {
            evalState = EvalState.FINISHED
        }
        manager.cancel()
    }

    /** 停止评测 */
    fun stop() {
        if (evalState == EvalState.RUNNING) {
            evalState = EvalState.FINISHED
        }
        manager.stop()
    }


}