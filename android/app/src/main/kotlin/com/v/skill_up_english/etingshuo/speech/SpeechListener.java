package com.v.skill_up_english.etingshuo.speech;

/**
 * <AUTHOR>
 * @date 2018/8/1 02:34
 */
public interface SpeechListener {
    int STATUS_READY = 0;

    void onUpdate(int speechStatus);
    void onVolumeChanged(int var1);
    void onBeginOfSpeech();
    void onEndOfSpeech();
    void onResult(SpeechResult result);
    void onError(int errCode,String errMsg);
    void onProgress(int progress,int total);
}
