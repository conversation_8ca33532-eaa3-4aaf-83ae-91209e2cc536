package com.v.skill_up_english.etingshuo.speech;

import android.os.Parcel;
import android.os.Parcelable;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @date 2018/8/2 02:46
 */
public class SpeechStress implements Parcelable {
    /**音素*/
    @JSONField(name="char")
    private String content;
    /**得分*/
    private float score;
    /**是否重读*/
    private int ref;

    public SpeechStress() {

    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public float getScore() {
        return score;
    }

    public void setScore(float score) {
        this.score = score;
    }

    public int getRef() {
        return ref;
    }

    public void setRef(int ref) {
        this.ref = ref;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.content);
        dest.writeFloat(this.score);
        dest.writeInt(this.ref);
    }



    protected SpeechStress(Parcel in) {
        this.content = in.readString();
        this.score = in.readFloat();
        this.ref = in.readInt();
    }

    public static final Creator<SpeechStress> CREATOR = new Creator<SpeechStress>() {
        @Override
        public SpeechStress createFromParcel(Parcel source) {
            return new SpeechStress(source);
        }

        @Override
        public SpeechStress[] newArray(int size) {
            return new SpeechStress[size];
        }
    };
}
