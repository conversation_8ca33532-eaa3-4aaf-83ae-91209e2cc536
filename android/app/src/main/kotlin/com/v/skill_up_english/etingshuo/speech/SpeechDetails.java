package com.v.skill_up_english.etingshuo.speech;

import android.os.Parcel;
import android.os.Parcelable;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/8/2 09:34
 */
public class SpeechDetails implements Parcelable {
     /**单词发音得分*/
     private int score;
     /**流利度评分(0-100)*/
     private float fluency = -1;

    /**单词发音时间*/
     private int dur;
     /**单词在音频中的起始时间，单位为毫秒（ms）*/
     private int start;
    /**单词在音频中的结束时间，单位为毫秒（ms）*/
    private int end;
    /**重读得分（0、1）*/
    private int stressscore;
    /**重读标识*/
    private int stressref;
    /**单词*/
    @JSONField(name="char")
     private String content;
    /**意群停顿标识*/
     private int senseref;
     /**意群停顿得分(0、1)*/
    private int sensescore;
    /**升调标识*/
     private int tonescore;
     /**意群停顿标识*/
     private int toneref;
     /**连读标识*/
     private int liaisonref;
     /**连读得分(0、1)*/
    private int liaisonscore;
    /**单词正常朗读（不输出dp_type 字段）、漏读（1）、重复读（2）*/
    private int dp_type;
    /**停顿标记*/
    private int is_pause;
    /**音素*/
    private List<SpeechPhone> phone;
    private List<SpeechStress> stress;

    private String mp3Url;
    private String phonetic;
    private String downUrl;

    public String getDownUrl() {
        return downUrl;
    }

    public void setDownUrl(String downUrl) {
        this.downUrl = downUrl;
    }

    public String getMp3Url() {
        return mp3Url;
    }

    public void setMp3Url(String mp3Url) {
        this.mp3Url = mp3Url;
    }

    public String getPhonetic() {
        return phonetic;
    }

    public void setPhonetic(String phonetic) {
        this.phonetic = phonetic;
    }

    @Override
    public String toString() {
        StringBuffer sb = new StringBuffer();
        String str = "";
        switch (dp_type) {
            case 0:
                str = " ";
                break;
            case 1:
                str = " [漏读] ";
                break;
            case 2:
                str = " [重复读] ";
                break;
            default:
                break;
        }

        if (score > 70) {
            sb.append("【单词】<font color='#37CE8D'>" + content + str + "</font><br/>");
        } else {
            sb.append("【单词】<font color='#EB5A5C'>" + content + str + "</font><br/>");
        }
        sb.append("&nbsp;&nbsp;总得分：&nbsp;&nbsp;" + score + "<br/>");
        if(fluency >= 0) {
            sb.append("&nbsp;&nbsp;流利度得分：" + fluency + "<br/>");
        }

        if (phone != null && !phone.isEmpty()) {
            for (int i = 0; i < phone.size(); i++) {
                sb.append("&nbsp;&nbsp;"+phone.get(i).toString() + "<br/>");
            }
        }
        return sb.toString();
    }

    public float getFluency() {
        return fluency;
    }

    public void setFluency(float fluency) {
        this.fluency = fluency;
    }

    public int getDur() {
        return dur;
    }

    public void setDur(int dur) {
        this.dur = dur;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public int getEnd() {
        return end;
    }

    public void setEnd(int end) {
        this.end = end;
    }

    public int getStressscore() {
        return stressscore;
    }

    public void setStressscore(int stressscore) {
        this.stressscore = stressscore;
    }

    public int getStressref() {
        return stressref;
    }

    public void setStressref(int stressref) {
        this.stressref = stressref;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getSenseref() {
        return senseref;
    }

    public void setSenseref(int senseref) {
        this.senseref = senseref;
    }

    public int getSensescore() {
        return sensescore;
    }

    public void setSensescore(int sensescore) {
        this.sensescore = sensescore;
    }

    public int getTonescore() {
        return tonescore;
    }

    public void setTonescore(int tonescore) {
        this.tonescore = tonescore;
    }

    public int getToneref() {
        return toneref;
    }

    public void setToneref(int toneref) {
        this.toneref = toneref;
    }

    public int getLiaisonref() {
        return liaisonref;
    }

    public void setLiaisonref(int liaisonref) {
        this.liaisonref = liaisonref;
    }

    public int getLiaisonscore() {
        return liaisonscore;
    }

    public void setLiaisonscore(int liaisonscore) {
        this.liaisonscore = liaisonscore;
    }

    public int getDp_type() {
        return dp_type;
    }

    public void setDp_type(int dp_type) {
        this.dp_type = dp_type;
    }

    public int getIs_pause() {
        return is_pause;
    }

    public void setIs_pause(int is_pause) {
        this.is_pause = is_pause;
    }

    public List<SpeechStress> getStress() {
        return stress;
    }

    public void setStress(List<SpeechStress> stress) {
        this.stress = stress;
    }

    public List<SpeechPhone> getPhone() {
        return phone;
    }

    public void setPhone(List<SpeechPhone> phone) {
        this.phone = phone;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.score);
        dest.writeFloat(this.fluency);
        dest.writeInt(this.dur);
        dest.writeInt(this.start);
        dest.writeInt(this.end);
        dest.writeInt(this.stressscore);
        dest.writeInt(this.stressref);
        dest.writeString(this.content);
        dest.writeInt(this.senseref);
        dest.writeInt(this.sensescore);
        dest.writeInt(this.tonescore);
        dest.writeInt(this.toneref);
        dest.writeInt(this.liaisonref);
        dest.writeInt(this.liaisonscore);
        dest.writeInt(this.dp_type);
        dest.writeInt(this.is_pause);
        dest.writeTypedList(this.phone);
        dest.writeTypedList(this.stress);
        dest.writeString(this.mp3Url);
        dest.writeString(this.phonetic);
        dest.writeString(this.downUrl);
    }

    public SpeechDetails() {
    }

    protected SpeechDetails(Parcel in) {
        this.score = in.readInt();
        this.fluency = in.readFloat();
        this.dur = in.readInt();
        this.start = in.readInt();
        this.end = in.readInt();
        this.stressscore = in.readInt();
        this.stressref = in.readInt();
        this.content = in.readString();
        this.senseref = in.readInt();
        this.sensescore = in.readInt();
        this.tonescore = in.readInt();
        this.toneref = in.readInt();
        this.liaisonref = in.readInt();
        this.liaisonscore = in.readInt();
        this.dp_type = in.readInt();
        this.is_pause = in.readInt();
        this.phone = in.createTypedArrayList(SpeechPhone.CREATOR);
        this.stress = in.createTypedArrayList(SpeechStress.CREATOR);
        this.mp3Url = in.readString();
        this.phonetic = in.readString();
        this.downUrl = in.readString();
    }

    public static final Creator<SpeechDetails> CREATOR = new Creator<SpeechDetails>() {
        @Override
        public SpeechDetails createFromParcel(Parcel source) {
            return new SpeechDetails(source);
        }

        @Override
        public SpeechDetails[] newArray(int size) {
            return new SpeechDetails[size];
        }
    };
}
