package com.v.skill_up_english.etingshuo

import java.lang.reflect.InvocationHandler
import java.lang.reflect.Proxy
import kotlin.jvm.java

/**
 *
 * <AUTHOR>
 * @date 2025/06/627*/

inline fun <reified T : Any> noOpDelegate(): T {
    val javaClass = T::class.java
    return Proxy.newProxyInstance(
        javaClass.classLoader, arrayOf(javaClass), NO_OP_HANDLER
    ) as T
}

val NO_OP_HANDLER = InvocationHandler { _, _, _ ->
    // no op
}