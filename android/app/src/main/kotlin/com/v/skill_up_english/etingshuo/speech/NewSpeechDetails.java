package com.v.skill_up_english.etingshuo.speech;

import android.os.Parcel;
import android.os.Parcelable;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * Created by linxi on 2022\7\1 0001.
 */
public class NewSpeechDetails implements Parcelable {

    private String text;
    private float score;
    private List<SntDetails> snt_details;

    public List<SntDetails> getSnt_details() {
        return snt_details;
    }

    public void setSnt_details(List<SntDetails> snt_details) {
        this.snt_details = snt_details;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public float getScore() {
        return score;
    }

    public void setScore(float score) {
        this.score = score;
    }

    public static class SntDetails implements Parcelable{

        public SntDetails(){

        }

        private int dp_type;
        @JSONField(name="char")
        private String content;
        private String text;
        private int dur;
        private int start;
        private float score;
        private int end;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public int getDp_type() {
            return dp_type;
        }

        public void setDp_type(int dp_type) {
            this.dp_type = dp_type;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
            this.text = content;
        }

        public int getDur() {
            return dur;
        }

        public void setDur(int dur) {
            this.dur = dur;
        }

        public int getStart() {
            return start;
        }

        public void setStart(int start) {
            this.start = start;
        }

        public float getScore() {
            return score;
        }

        public void setScore(float score) {
            this.score = score;
        }

        public int getEnd() {
            return end;
        }

        public void setEnd(int end) {
            this.end = end;
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeInt(this.dp_type);
            dest.writeString(this.content);
            dest.writeString(this.text);
            dest.writeInt(this.dur);
            dest.writeInt(this.start);
            dest.writeFloat(this.score);
            dest.writeInt(this.end);
        }

        public void readFromParcel(Parcel source) {
            this.dp_type = source.readInt();
            this.content = source.readString();
            this.text = source.readString();
            this.dur = source.readInt();
            this.start = source.readInt();
            this.score = source.readFloat();
            this.end = source.readInt();
        }

        protected SntDetails(Parcel in) {
            this.dp_type = in.readInt();
            this.content = in.readString();
            this.text = in.readString();
            this.dur = in.readInt();
            this.start = in.readInt();
            this.score = in.readFloat();
            this.end = in.readInt();
        }

        public static final Creator<SntDetails> CREATOR = new Creator<SntDetails>() {
            @Override
            public SntDetails createFromParcel(Parcel source) {
                return new SntDetails(source);
            }

            @Override
            public SntDetails[] newArray(int size) {
                return new SntDetails[size];
            }
        };
    }

    public NewSpeechDetails() {

    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.text);
        dest.writeFloat(this.score);
        dest.writeTypedList(this.snt_details);
    }

    public void readFromParcel(Parcel source) {
        this.text = source.readString();
        this.score = source.readFloat();
        this.snt_details = source.createTypedArrayList(SntDetails.CREATOR);
    }

    protected NewSpeechDetails(Parcel in) {
        this.text = in.readString();
        this.score = in.readFloat();
        this.snt_details = in.createTypedArrayList(SntDetails.CREATOR);
    }

    public static final Creator<NewSpeechDetails> CREATOR = new Creator<NewSpeechDetails>() {
        @Override
        public NewSpeechDetails createFromParcel(Parcel source) {
            return new NewSpeechDetails(source);
        }

        @Override
        public NewSpeechDetails[] newArray(int size) {
            return new NewSpeechDetails[size];
        }
    };
}
