package com.v.skill_up_english.etingshuo.speech;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

/**
 * Created by linxi on 2020/3/10.
 */

public class SpeechCnDetails implements Parcelable{

    public SpeechCnDetails(){

    }
    private String text;
    private int score;
    private Object fluency;
    private String chn_char;
    private List<SntDetailsBean> snt_details;

    protected SpeechCnDetails(Parcel in) {
        text = in.readString();
        score = in.readInt();
        fluency = in.readParcelable(Object.class.getClassLoader());
        chn_char = in.readString();
        snt_details = in.createTypedArrayList(SntDetailsBean.CREATOR);
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(text);
        dest.writeInt(score);
        dest.writeParcelable((Parcelable) fluency, flags);
        dest.writeString(chn_char);
        dest.writeTypedList(snt_details);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<SpeechCnDetails> CREATOR = new Creator<SpeechCnDetails>() {
        @Override
        public SpeechCnDetails createFromParcel(Parcel in) {
            return new SpeechCnDetails(in);
        }

        @Override
        public SpeechCnDetails[] newArray(int size) {
            return new SpeechCnDetails[size];
        }
    };

    public String getChn_char() {
        return chn_char;
    }

    public void setChn_char(String chn_char) {
        this.chn_char = chn_char;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public Object getFluency() {
        return fluency;
    }

    public void setFluency(Object fluency) {
        this.fluency = fluency;
    }

    public List<SntDetailsBean> getSnt_details() {
        return snt_details;
    }

    public void setSnt_details(List<SntDetailsBean> snt_details) {
        this.snt_details = snt_details;
    }

    public static class FluencyBean implements Parcelable{

        public FluencyBean(){

        }

        private float pause;
        private float overall;
        private float speed;

        protected FluencyBean(Parcel in) {
            pause = in.readFloat();
            overall = in.readFloat();
            speed = in.readFloat();
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeFloat(pause);
            dest.writeFloat(overall);
            dest.writeFloat(speed);
        }

        @Override
        public int describeContents() {
            return 0;
        }

        public static final Creator<FluencyBean> CREATOR = new Creator<FluencyBean>() {
            @Override
            public FluencyBean createFromParcel(Parcel in) {
                return new FluencyBean(in);
            }

            @Override
            public FluencyBean[] newArray(int size) {
                return new FluencyBean[size];
            }
        };

        public float getPause() {
            return pause;
        }

        public void setPause(float pause) {
            this.pause = pause;
        }

        public float getOverall() {
            return overall;
        }

        public void setOverall(float overall) {
            this.overall = overall;
        }

        public float getSpeed() {
            return speed;
        }

        public void setSpeed(float speed) {
            this.speed = speed;
        }
    }

    public static class SntDetailsBean implements Parcelable{
        public SntDetailsBean(){

        }
        private int dp_type;
        private float tonescore;
        private int dur;
        private String chn_char;
        private int start;
        private float score;
        private int tone;
        private int end;

        protected SntDetailsBean(Parcel in) {
            dp_type = in.readInt();
            tonescore = in.readFloat();
            dur = in.readInt();
            chn_char = in.readString();
            start = in.readInt();
            score = in.readFloat();
            tone = in.readInt();
            end = in.readInt();
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeInt(dp_type);
            dest.writeFloat(tonescore);
            dest.writeInt(dur);
            dest.writeString(chn_char);
            dest.writeInt(start);
            dest.writeFloat(score);
            dest.writeInt(tone);
            dest.writeInt(end);
        }

        @Override
        public int describeContents() {
            return 0;
        }

        public static final Creator<SntDetailsBean> CREATOR = new Creator<SntDetailsBean>() {
            @Override
            public SntDetailsBean createFromParcel(Parcel in) {
                return new SntDetailsBean(in);
            }

            @Override
            public SntDetailsBean[] newArray(int size) {
                return new SntDetailsBean[size];
            }
        };

        public int getDp_type() {
            return dp_type;
        }

        public void setDp_type(int dp_type) {
            this.dp_type = dp_type;
        }

        public float getTonescore() {
            return tonescore;
        }

        public void setTonescore(float tonescore) {
            this.tonescore = tonescore;
        }

        public int getDur() {
            return dur;
        }

        public void setDur(int dur) {
            this.dur = dur;
        }

        public String getChn_char() {
            return chn_char;
        }

        public void setChn_char(String chn_char) {
            this.chn_char = chn_char;
        }

        public int getStart() {
            return start;
        }

        public void setStart(int start) {
            this.start = start;
        }

        public float getScore() {
            return score;
        }

        public void setScore(float score) {
            this.score = score;
        }

        public int getTone() {
            return tone;
        }

        public void setTone(int tone) {
            this.tone = tone;
        }

        public int getEnd() {
            return end;
        }

        public void setEnd(int end) {
            this.end = end;
        }
    }


}
