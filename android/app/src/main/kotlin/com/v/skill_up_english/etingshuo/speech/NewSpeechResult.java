package com.v.skill_up_english.etingshuo.speech;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

/**
 * 英文段落评测结果
 *
 * <AUTHOR>
 * @date 2018/8/2 09:20
 */
public class NewSpeechResult implements Parcelable {
    /**
     * 评测整个过程的总耗时，单位ms
     */
    private int systime;
    /**
     * 云端调用Start 接口本身耗时，单位ms
     */
    private int pretime;
    /**
     * 云端从feed 音频结束到获取结果的耗时，单位ms
     */
    private int delaytime;
    /**
     * 云端调用Start 接口本身耗时，单位ms
     */
    private int precision;

    /**
     * 评分分制
     */
    private float rank;
    /**
     * 评测本题型时，使用的资源名称
     */
    private String res;
    /**
     * 音频时长，单位ms
     */
    private int wavetime;
    /**
     * 停顿次数
     */
    private int pause;
    /**
     * 语速快慢，0：慢，1：正常，2：快
     */
    private int speed;

    /**
     * 完整度评分
     */
    private float integrity;
    /**
     * 总分
     */
    private float overall;
    /**
     * 发音得分
     */
    private float pron;
    /**
     * 发音得分 准确度
     */
    private float accuracy;

    private String audioUrl;


    /**
     * 流利度评分
     */
    private float fluency;
    /**
     * 音素
     */
    private List<SpeechStatics> statics;
    /**
     * 韵律性评分
     */
    private SpeechRhythm rhythm;
    /**
     * 单词详细得分
     */
    private List<NewSpeechDetails> details;
    /**
     * 信噪
     */
    private SpeechInfo info;

    public float getFluency() {
        return fluency;
    }

    public void setFluency(float fluency) {
        this.fluency = fluency;
    }

    public String getAudioUrl() {
        return audioUrl;
    }

    public void setAudioUrl(String audioUrl) {
        this.audioUrl = audioUrl;
    }

    public int getSystime() {
        return systime;
    }

    public void setSystime(int systime) {
        this.systime = systime;
    }

    public int getPretime() {
        return pretime;
    }

    public void setPretime(int pretime) {
        this.pretime = pretime;
    }

    public int getDelaytime() {
        return delaytime;
    }

    public void setDelaytime(int delaytime) {
        this.delaytime = delaytime;
    }

    public int getPrecision() {
        return precision;
    }

    public void setPrecision(int precision) {
        this.precision = precision;
    }

    public float getRank() {
        return rank;
    }

    public void setRank(float rank) {
        this.rank = rank;
    }

    public String getRes() {
        return res;
    }

    public void setRes(String res) {
        this.res = res;
    }

    public int getWavetime() {
        return wavetime;
    }

    public void setWavetime(int wavetime) {
        this.wavetime = wavetime;
    }

    public float getIntegrity() {
        return integrity;
    }

    public void setIntegrity(float integrity) {
        this.integrity = integrity;
    }

    public float getPron() {
        return pron;
    }

    public void setPron(float pron) {
        this.pron = pron;
    }

    public float getOverall() {
        return overall;
    }

    public void setOverall(float overall) {
        this.overall = overall;
    }

    public int getPause() {
        return pause;
    }

    public void setPause(int pause) {
        this.pause = pause;
    }

    public int getSpeed() {
        return speed;
    }

    public void setSpeed(int speed) {
        this.speed = speed;
    }

    public float getAccuracy() {
        return accuracy;
    }

    public void setAccuracy(float accuracy) {
        this.accuracy = (float) (Math.round(accuracy * 100) / 100);
    }



    public List<SpeechStatics> getStatics() {
        return statics;
    }

    public void setStatics(List<SpeechStatics> statics) {
        this.statics = statics;
    }

    public SpeechRhythm getRhythm() {
        return rhythm;
    }

    public void setRhythm(SpeechRhythm rhythm) {
        this.rhythm = rhythm;
    }

    public List<NewSpeechDetails> getDetails() {
        return details;
    }

    public void setDetails(List<NewSpeechDetails> details) {
        this.details = details;
    }

    public SpeechInfo getInfo() {
        return info;
    }

    public void setInfo(SpeechInfo info) {
        this.info = info;
    }


    public NewSpeechResult() {
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.systime);
        dest.writeInt(this.pretime);
        dest.writeInt(this.delaytime);
        dest.writeInt(this.precision);
        dest.writeFloat(this.rank);
        dest.writeString(this.res);
        dest.writeInt(this.wavetime);
        dest.writeInt(this.pause);
        dest.writeInt(this.speed);
        dest.writeFloat(this.integrity);
        dest.writeFloat(this.overall);
        dest.writeFloat(this.pron);
        dest.writeFloat(this.accuracy);
        dest.writeString(this.audioUrl);
        dest.writeFloat(this.fluency);
        dest.writeTypedList(this.statics);
        dest.writeParcelable(this.rhythm, flags);
        dest.writeTypedList(this.details);
        dest.writeParcelable(this.info, flags);
    }

    protected NewSpeechResult(Parcel in) {
        this.systime = in.readInt();
        this.pretime = in.readInt();
        this.delaytime = in.readInt();
        this.precision = in.readInt();
        this.rank = in.readFloat();
        this.res = in.readString();
        this.wavetime = in.readInt();
        this.pause = in.readInt();
        this.speed = in.readInt();
        this.integrity = in.readFloat();
        this.overall = in.readFloat();
        this.pron = in.readFloat();
        this.accuracy = in.readFloat();
        this.audioUrl = in.readString();
        this.fluency = in.readFloat();
        this.statics = in.createTypedArrayList(SpeechStatics.CREATOR);
        this.rhythm = in.readParcelable(SpeechRhythm.class.getClassLoader());
        this.details = in.createTypedArrayList(NewSpeechDetails.CREATOR);
        this.info = in.readParcelable(SpeechInfo.class.getClassLoader());
    }

    public static final Creator<NewSpeechResult> CREATOR = new Creator<NewSpeechResult>() {
        @Override
        public NewSpeechResult createFromParcel(Parcel source) {
            return new NewSpeechResult(source);
        }

        @Override
        public NewSpeechResult[] newArray(int size) {
            return new NewSpeechResult[size];
        }
    };
}
