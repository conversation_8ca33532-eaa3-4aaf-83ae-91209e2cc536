package com.v.skill_up_english.etingshuo.speech;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 韵律性评分
 * <AUTHOR>
 * @date 2018/8/2 09:32
 */
public class SpeechRhythm implements Parcelable {
    /**重音得分(0-100) */
     private float stress;
     /**韵律总体得分(sense 占50%，stress 和tone各占25%) (0-100)*/
    private float overall;
    /**意群得分(0-100)*/
    private float sense;
    /**句子升降调得分(0-100)*/
    private float tone;

    public float getStress() {
        return stress;
    }

    public void setStress(float stress) {
        this.stress = stress;
    }

    public float getOverall() {
        return overall;
    }

    public void setOverall(float overall) {
        this.overall = overall;
    }

    public float getSense() {
        return sense;
    }

    public void setSense(float sense) {
        this.sense = sense;
    }

    public float getTone() {
        return tone;
    }

    public void setTone(float tone) {
        this.tone = tone;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeFloat(this.stress);
        dest.writeFloat(this.overall);
        dest.writeFloat(this.sense);
        dest.writeFloat(this.tone);
    }

    public SpeechRhythm() {
    }

    protected SpeechRhythm(Parcel in) {
        this.stress = in.readFloat();
        this.overall = in.readFloat();
        this.sense = in.readFloat();
        this.tone = in.readFloat();
    }

    public static final Creator<SpeechRhythm> CREATOR = new Creator<SpeechRhythm>() {
        @Override
        public SpeechRhythm createFromParcel(Parcel source) {
            return new SpeechRhythm(source);
        }

        @Override
        public SpeechRhythm[] newArray(int size) {
            return new SpeechRhythm[size];
        }
    };
}
