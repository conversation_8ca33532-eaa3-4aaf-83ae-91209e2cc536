package com.v.skill_up_english.etingshuo.speech;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

/**
 * Created by linxi on 2020/3/10.
 */

public class SpeechCnResult implements Parcelable {

    public SpeechCnResult(){

    }
    /*** 评测整个过程的总耗时，单位ms*/
    private int systime;
    /*** 云端调用Start 接口本身耗时，单位ms*/
    private int pretime;
    /*** 云端从feed 音频结束到获取结果的耗时，单位ms*/
    private int delaytime;
    /*** 云端调用Start 接口本身耗时，单位ms*/
    private int precision;
    /*** 评分分制*/
    private float rank;
    /*** 评测本题型时，使用的资源名称*/
    private String res;
    /*** 音频时长，单位ms*/
    private int wavetime;
    /*** 完整度评分*/
    private float integrity;
    /*** 总分*/
    private float overall;
    /*** 发音得分*/
    private float pron;
    /*** 发音得分 准确度*/
    private float accuracy;
    /*** 流利度评分*/
    private Object fluency;
    /*** 平均语速 */
    private float mSpeed;
    /*** 单词详细得分*/
    private List<SpeechCnDetails> details;
    /*** 信噪*/
    private SpeechInfo info;
    private String audioUrl;

    protected SpeechCnResult(Parcel in) {
        systime = in.readInt();
        pretime = in.readInt();
        delaytime = in.readInt();
        precision = in.readInt();
        rank = in.readFloat();
        res = in.readString();
        wavetime = in.readInt();
        integrity = in.readFloat();
        overall = in.readFloat();
        pron = in.readFloat();
        accuracy = in.readFloat();
        fluency = in.readParcelable(Object.class.getClassLoader());
        mSpeed = in.readFloat();
        details = in.createTypedArrayList(SpeechCnDetails.CREATOR);
        info = in.readParcelable(SpeechInfo.class.getClassLoader());
        audioUrl = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(systime);
        dest.writeInt(pretime);
        dest.writeInt(delaytime);
        dest.writeInt(precision);
        dest.writeFloat(rank);
        dest.writeString(res);
        dest.writeInt(wavetime);
        dest.writeFloat(integrity);
        dest.writeFloat(overall);
        dest.writeFloat(pron);
        dest.writeFloat(accuracy);
        dest.writeParcelable((Parcelable) fluency, flags);
        dest.writeFloat(mSpeed);
        dest.writeTypedList(details);
        dest.writeParcelable(info, flags);
        dest.writeString(audioUrl);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<SpeechCnResult> CREATOR = new Creator<SpeechCnResult>() {
        @Override
        public SpeechCnResult createFromParcel(Parcel in) {
            return new SpeechCnResult(in);
        }

        @Override
        public SpeechCnResult[] newArray(int size) {
            return new SpeechCnResult[size];
        }
    };

    public String getAudioUrl() {
        return audioUrl;
    }

    public void setAudioUrl(String audioUrl) {
        this.audioUrl = audioUrl;
    }

    public int getSystime() {
        return systime;
    }

    public void setSystime(int systime) {
        this.systime = systime;
    }

    public int getPretime() {
        return pretime;
    }

    public void setPretime(int pretime) {
        this.pretime = pretime;
    }

    public int getDelaytime() {
        return delaytime;
    }

    public void setDelaytime(int delaytime) {
        this.delaytime = delaytime;
    }

    public int getPrecision() {
        return precision;
    }

    public void setPrecision(int precision) {
        this.precision = precision;
    }

    public float getRank() {
        return rank;
    }

    public void setRank(float rank) {
        this.rank = rank;
    }

    public String getRes() {
        return res;
    }

    public void setRes(String res) {
        this.res = res;
    }

    public int getWavetime() {
        return wavetime;
    }

    public void setWavetime(int wavetime) {
        this.wavetime = wavetime;
    }

    public float getIntegrity() {
        return integrity;
    }

    public void setIntegrity(float integrity) {
        this.integrity = integrity;
    }

    public float getOverall() {
        return overall;
    }

    public void setOverall(float overall) {
        this.overall = overall;
    }

    public float getPron() {
        return pron;
    }

    public void setPron(float pron) {
        this.pron = pron;
    }

    public float getAccuracy() {
        return accuracy;
    }

    public void setAccuracy(float accuracy) {
        this.accuracy = accuracy;
    }

    public Object getFluency() {
        return fluency;
    }

    public void setFluency(Object fluency) {
        this.fluency = fluency;
    }

    public float getSpeed() {
        return mSpeed;
    }

    public void setSpeed(float speed) {
        this.mSpeed = speed;
    }

    public List<SpeechCnDetails> getDetails() {
        return details;
    }

    public void setDetails(List<SpeechCnDetails> details) {
        this.details = details;
    }

    public SpeechInfo getInfo() {
        return info;
    }

    public void setInfo(SpeechInfo info) {
        this.info = info;
    }
}
