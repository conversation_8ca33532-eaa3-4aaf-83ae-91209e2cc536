package com.v.skill_up_english.etingshuo.speech;

import android.os.Parcel;
import android.os.Parcelable;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @date 2018/8/2 02:46
 */
public class SpeechPhone implements Parcelable {
    /**音素*/
    @JSONField(name="char")
    private String content;
    private String ph2alpha;
    /**音素发音得分*/
    private float score;
    /**此音标在当前单词音标中的位置*/
    private String phid;
    /**音素检错的结果*/
    private int pherr;

    @Override
    public String toString() {
       String str = "";
//       if(score > 70){
//           str = "【音素】<font color='#2bd06e'>" + content + " <font style='font-size:0.8em'>[得分：" + score + "]</font></font>";
//       }else {
//           str = "【音素】<font color='#F94165'>" + content + " <font style='font-size:0.8em'>[得分：" + score + "]</font></font>";
//       }
        str = "【音素】" + content + " [得分：" + score + "]";
        return str;
    }

    public String getPh2alpha() {
        return ph2alpha;
    }

    public void setPh2alpha(String ph2alpha) {
        this.ph2alpha = ph2alpha;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public float getScore() {
        return score;
    }

    public void setScore(float score) {
        this.score = score;
    }

    public String getPhid() {
        return phid;
    }

    public void setPhid(String phid) {
        this.phid = phid;
    }

    public int getPherr() {
        return pherr;
    }

    public void setPherr(int pherr) {
        this.pherr = pherr;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.content);
        dest.writeString(this.ph2alpha);
        dest.writeFloat(this.score);
        dest.writeString(this.phid);
        dest.writeInt(this.pherr);
    }

    public SpeechPhone() {
    }

    protected SpeechPhone(Parcel in) {
        this.content = in.readString();
        this.ph2alpha = in.readString();
        this.score = in.readFloat();
        this.phid = in.readString();
        this.pherr = in.readInt();
    }

    public static final Creator<SpeechPhone> CREATOR = new Creator<SpeechPhone>() {
        @Override
        public SpeechPhone createFromParcel(Parcel source) {
            return new SpeechPhone(source);
        }

        @Override
        public SpeechPhone[] newArray(int size) {
            return new SpeechPhone[size];
        }
    };
}
