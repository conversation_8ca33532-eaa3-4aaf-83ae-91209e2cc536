package com.v.skill_up_english.etingshuo.speech;

import android.os.Parcel;
import android.os.Parcelable;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * <AUTHOR>
 * @date 2018/8/2 09:30
 */
public class SpeechStatics implements Parcelable {
    /**音素*/
    @JSONField(name="char")
     private String content;
     /**该音素出现的次数*/
     private int count;
     /**音素平均发音得分(0-100)*/
    private int score;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.content);
        dest.writeInt(this.count);
        dest.writeInt(this.score);
    }

    public SpeechStatics() {
    }

    protected SpeechStatics(Parcel in) {
        this.content = in.readString();
        this.count = in.readInt();
        this.score = in.readInt();
    }

    public static final Creator<SpeechStatics> CREATOR = new Creator<SpeechStatics>() {
        @Override
        public SpeechStatics createFromParcel(Parcel source) {
            return new SpeechStatics(source);
        }

        @Override
        public SpeechStatics[] newArray(int size) {
            return new SpeechStatics[size];
        }
    };
}
