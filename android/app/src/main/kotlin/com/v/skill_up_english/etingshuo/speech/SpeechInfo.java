package com.v.skill_up_english.etingshuo.speech;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * @date 2018/8/2 09:43
 */
public class SpeechInfo implements Parcelable {
    /**录音音量，范围（0～180dB）*/
     private int volume;
    /**音频声音太高，出现截幅，范围(0~1)。*/
    private float clip;
    /**信噪比，值越高越清晰，范围(0～40dB)*/
    private float snr;
    /**音频质量*/
    private int tipId;

    public int getVolume() {
        return volume;
    }

    public void setVolume(int volume) {
        this.volume = volume;
    }

    public float getClip() {
        return clip;
    }

    public void setClip(float clip) {
        this.clip = clip;
    }

    public float getSnr() {
        return snr;
    }

    public void setSnr(float snr) {
        this.snr = snr;
    }

    public int getTipId() {
        return tipId;
    }

    public void setTipId(int tipId) {
        this.tipId = tipId;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.volume);
        dest.writeFloat(this.clip);
        dest.writeFloat(this.snr);
        dest.writeInt(this.tipId);
    }

    public SpeechInfo() {
    }

    protected SpeechInfo(Parcel in) {
        this.volume = in.readInt();
        this.clip = in.readFloat();
        this.snr = in.readFloat();
        this.tipId = in.readInt();
    }

    public static final Creator<SpeechInfo> CREATOR = new Creator<SpeechInfo>() {
        @Override
        public SpeechInfo createFromParcel(Parcel source) {
            return new SpeechInfo(source);
        }

        @Override
        public SpeechInfo[] newArray(int size) {
            return new SpeechInfo[size];
        }
    };
}
