package com.v.skill_up_english.etingshuo.speech;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 流利度评分
 * <AUTHOR>
 * @date 2018/8/2 09:41
 */
public class SpeechFluency implements Parcelable {
    /**停顿次数*/
   private int pause;
    /**流利度总体得分（0-100）*/
   private float overall;
   /**语速快慢，0：慢，1：正常，2：快*/
    private int speed;

    public String getSpeedStr(){
        String str= "正常";
        switch (speed) {
            case 0:
                str = "较慢";
                break;
            case 1:
                str = "正常";
                break;
            case 2:
                str = "较快";
                break;
            default:
                break;
        }
        return str;
    }

    public int getPause() {
        return pause;
    }

    public void setPause(int pause) {
        this.pause = pause;
    }

    public float getOverall() {
        return overall;
    }

    public void setOverall(float overall) {
        this.overall = overall;
    }

    public int getSpeed() {
        return speed;
    }

    public void setSpeed(int speed) {
        this.speed = speed;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.pause);
        dest.writeFloat(this.overall);
        dest.writeInt(this.speed);
    }

    public SpeechFluency() {
    }

    protected SpeechFluency(Parcel in) {
        this.pause = in.readInt();
        this.overall = in.readFloat();
        this.speed = in.readInt();
    }

    public static final Creator<SpeechFluency> CREATOR = new Creator<SpeechFluency>() {
        @Override
        public SpeechFluency createFromParcel(Parcel source) {
            return new SpeechFluency(source);
        }

        @Override
        public SpeechFluency[] newArray(int size) {
            return new SpeechFluency[size];
        }
    };
}
