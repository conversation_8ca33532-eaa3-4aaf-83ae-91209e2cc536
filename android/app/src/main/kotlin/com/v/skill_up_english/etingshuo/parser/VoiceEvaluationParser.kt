package com.iflytek.edu.ai.demo.parser

import com.v.skill_up_english.etingshuo.speech.SpeechDetails
import com.v.skill_up_english.etingshuo.speech.SpeechFluency
import com.v.skill_up_english.etingshuo.speech.SpeechPhone
import com.v.skill_up_english.etingshuo.speech.SpeechResult
import org.xmlpull.v1.XmlPullParser
import org.xmlpull.v1.XmlPullParserFactory
import java.io.StringReader


/**
 * 语音评测结果解析器
 * 用于解析语音评测XML文件，包含阅读章节的评测数据
 */
class VoiceEvaluationParser {

    // 记录无法映射的字段
    private val skippedFields = mutableSetOf<String>()

    /**
     * 解析XML字符串为语音评测结果对象
     * @param xmlString XML字符串内容
     * @return 解析后的语音评测结果
     */
    fun parse(xmlString: String): VoiceEvaluationResult {
        val factory = XmlPullParserFactory.newInstance()
        factory.isNamespaceAware = true
        val parser = factory.newPullParser()
        parser.setInput(StringReader(xmlString))

        return parseVoiceEvaluationResult(parser)
    }

    /**
     * 解析语音评测结果根节点
     */
    private fun parseVoiceEvaluationResult(parser: XmlPullParser): VoiceEvaluationResult {
        var eventType = parser.eventType
        var result = VoiceEvaluationResult()

        while (eventType != XmlPullParser.END_DOCUMENT) {
            when (eventType) {
                XmlPullParser.START_TAG -> {
                    when (parser.name) {
                        "xml_result" -> {
                            // 根节点，继续解析子元素
                        }
                        "read_chapter" -> {
                            result.readChapter = parseReadChapter(parser)
                        }
                    }
                }
            }
            eventType = parser.next()
        }

        return result
    }

    /**
     * 解析阅读章节节点
     */
    private fun parseReadChapter(parser: XmlPullParser): ReadChapter {
        val readChapter = ReadChapter()

        // 解析属性
        for (i in 0 until parser.attributeCount) {
            when (parser.getAttributeName(i)) {
                "lan" -> readChapter.language = parser.getAttributeValue(i)
                "type" -> readChapter.type = parser.getAttributeValue(i)
                "version" -> readChapter.version = parser.getAttributeValue(i)
            }
        }

        var eventType = parser.next()
        while (eventType != XmlPullParser.END_TAG || parser.name != "read_chapter") {
            if (eventType == XmlPullParser.START_TAG && parser.name == "rec_paper") {
                readChapter.recPaper = parseRecPaper(parser)
            }
            eventType = parser.next()
        }

        return readChapter
    }

    /**
     * 解析录音试卷节点
     */
    private fun parseRecPaper(parser: XmlPullParser): RecPaper {
        val recPaper = RecPaper()

        var eventType = parser.next()
        while (eventType != XmlPullParser.END_TAG || parser.name != "rec_paper") {
            if (eventType == XmlPullParser.START_TAG && parser.name == "read_chapter") {
                recPaper.readChapter = parseReadChapterDetail(parser)
            }
            eventType = parser.next()
        }

        return recPaper
    }

    /**
     * 解析阅读章节详细信息
     */
    private fun parseReadChapterDetail(parser: XmlPullParser): ReadChapterDetail {
        val readChapter = ReadChapterDetail()

        // 解析属性
        for (i in 0 until parser.attributeCount) {
            when (parser.getAttributeName(i)) {
                "accuracy_score" -> readChapter.accuracyScore = parser.getAttributeValue(i).toFloatOrNull()
                "beg_pos" -> readChapter.begPos = parser.getAttributeValue(i).toIntOrNull()
                "content" -> readChapter.content = parser.getAttributeValue(i)
                "end_pos" -> readChapter.endPos = parser.getAttributeValue(i).toIntOrNull()
                "except_info" -> readChapter.exceptInfo = parser.getAttributeValue(i).toIntOrNull()
                "fluency_score" -> readChapter.fluencyScore = parser.getAttributeValue(i).toFloatOrNull()
                "integrity_score" -> readChapter.integrityScore = parser.getAttributeValue(i).toFloatOrNull()
                "is_rejected" -> readChapter.isRejected = parser.getAttributeValue(i).toBoolean()
                "reject_type" -> readChapter.rejectType = parser.getAttributeValue(i).toIntOrNull()
                "score_pattern" -> readChapter.scorePattern = parser.getAttributeValue(i)
                "standard_score" -> readChapter.standardScore = parser.getAttributeValue(i).toFloatOrNull()
                "total_score" -> readChapter.totalScore = parser.getAttributeValue(i).toFloatOrNull()
                "word_count" -> readChapter.wordCount = parser.getAttributeValue(i).toIntOrNull()
                "words_per_min" -> readChapter.wordsPerMin = parser.getAttributeValue(i).toFloatOrNull()
            }
        }

        var eventType = parser.next()
        while (eventType != XmlPullParser.END_TAG || parser.name != "read_chapter") {
            if (eventType == XmlPullParser.START_TAG && parser.name == "sentence") {
                readChapter.sentences.add(parseSentence(parser))
            }
            eventType = parser.next()
        }

        return readChapter
    }

    /**
     * 解析句子节点
     */
    private fun parseSentence(parser: XmlPullParser): Sentence {
        val sentence = Sentence()

        // 解析属性
        for (i in 0 until parser.attributeCount) {
            when (parser.getAttributeName(i)) {
                "accuracy_score" -> sentence.accuracyScore = parser.getAttributeValue(i).toFloatOrNull()
                "beg_pos" -> sentence.begPos = parser.getAttributeValue(i).toIntOrNull()
                "content" -> sentence.content = parser.getAttributeValue(i)
                "end_pos" -> sentence.endPos = parser.getAttributeValue(i).toIntOrNull()
                "fluency_score" -> sentence.fluencyScore = parser.getAttributeValue(i).toFloatOrNull()
                "index" -> sentence.index = parser.getAttributeValue(i).toIntOrNull()
                "standard_score" -> sentence.standardScore = parser.getAttributeValue(i).toFloatOrNull()
                "total_score" -> sentence.totalScore = parser.getAttributeValue(i).toFloatOrNull()
                "word_count" -> sentence.wordCount = parser.getAttributeValue(i).toIntOrNull()
            }
        }

        var eventType = parser.next()
        while (eventType != XmlPullParser.END_TAG || parser.name != "sentence") {
            if (eventType == XmlPullParser.START_TAG && parser.name == "word") {
                sentence.words.add(parseWord(parser))
            }
            eventType = parser.next()
        }

        return sentence
    }

    /**
     * 解析单词节点
     */
    private fun parseWord(parser: XmlPullParser): Word {
        val word = Word()

        // 解析属性
        for (i in 0 until parser.attributeCount) {
            when (parser.getAttributeName(i)) {
                "beg_pos" -> word.begPos = parser.getAttributeValue(i).toIntOrNull()
                "content" -> word.content = parser.getAttributeValue(i)
                "dp_message" -> word.dpMessage = parser.getAttributeValue(i).toIntOrNull()
                "end_pos" -> word.endPos = parser.getAttributeValue(i).toIntOrNull()
                "global_index" -> word.globalIndex = parser.getAttributeValue(i).toIntOrNull()
                "index" -> word.index = parser.getAttributeValue(i).toIntOrNull()
                "property" -> word.property = parser.getAttributeValue(i).toIntOrNull()
                "total_score" -> word.totalScore = parser.getAttributeValue(i).toFloatOrNull()
            }
        }

        var eventType = parser.next()
        while (eventType != XmlPullParser.END_TAG || parser.name != "word") {
            if (eventType == XmlPullParser.START_TAG && parser.name == "syll") {
                word.syllables.add(parseSyllable(parser))
            }
            eventType = parser.next()
        }

        return word
    }

    /**
     * 解析音节节点
     */
    private fun parseSyllable(parser: XmlPullParser): Syllable {
        val syllable = Syllable()

        // 解析属性
        for (i in 0 until parser.attributeCount) {
            when (parser.getAttributeName(i)) {
                "beg_pos" -> syllable.begPos = parser.getAttributeValue(i).toIntOrNull()
                "content" -> syllable.content = parser.getAttributeValue(i)
                "end_pos" -> syllable.endPos = parser.getAttributeValue(i).toIntOrNull()
                "serr_msg" -> syllable.serrMsg = parser.getAttributeValue(i).toIntOrNull()
                "syll_accent" -> syllable.syllAccent = parser.getAttributeValue(i).toIntOrNull()
                "syll_score" -> syllable.syllScore = parser.getAttributeValue(i).toFloatOrNull()
            }
        }

        var eventType = parser.next()
        while (eventType != XmlPullParser.END_TAG || parser.name != "syll") {
            if (eventType == XmlPullParser.START_TAG && parser.name == "phone") {
                syllable.phones.add(parsePhone(parser))
            }
            eventType = parser.next()
        }

        return syllable
    }

    /**
     * 解析音素节点
     */
    private fun parsePhone(parser: XmlPullParser): Phone {
        val phone = Phone()

        // 解析属性
        for (i in 0 until parser.attributeCount) {
            when (parser.getAttributeName(i)) {
                "beg_pos" -> phone.begPos = parser.getAttributeValue(i).toIntOrNull()
                "content" -> phone.content = parser.getAttributeValue(i)
                "end_pos" -> phone.endPos = parser.getAttributeValue(i).toIntOrNull()
                "gwpp" -> phone.gwpp = parser.getAttributeValue(i).toFloatOrNull()
                "phone_total_score" -> phone.phoneTotalScore = parser.getAttributeValue(i).toFloatOrNull()
            }
        }

        // 音素节点是自闭合的，直接移动到下一个节点
        parser.next()

        return phone
    }

    /**
     * 将VoiceEvaluationResult转换为SpeechResult
     * @param voiceResult XML解析结果
     * @return 转换后的SpeechResult
     */
    fun convertToSpeechResult(voiceResult: VoiceEvaluationResult): SpeechResult {
        val speechResult = SpeechResult()
        
        // 获取主要评测数据
        val readChapter = voiceResult.readChapter?.recPaper?.readChapter
        
        if (readChapter != null) {
            // 直接映射的字段
            speechResult.overall = readChapter.totalScore ?: 0f
            speechResult.accuracy = readChapter.accuracyScore ?: 0f
            speechResult.integrity = readChapter.integrityScore ?: 0f
            speechResult.pron = readChapter.accuracyScore ?: 0f // 发音得分通常等于准确度得分
            
            // 流利度相关
            speechResult.fluency = SpeechFluency().apply {
                overall = readChapter.fluencyScore ?: 0f
                // 注意：XML中没有直接的停顿次数和语速信息，需要从其他字段推断或设为默认值
                pause = 0 // 无法从XML直接获取
                speed = 1 // 默认正常语速
            }
            
            // 转换单词详情为SpeechDetails
            val details = mutableListOf<SpeechDetails>()
            readChapter.sentences.forEach { sentence ->
                sentence.words.forEach { word ->
                    val speechDetail = SpeechDetails().apply {
                        content = word.content ?: ""
                        score = (word.totalScore ?: 0f).toInt()
                        start = word.begPos ?: 0
                        end = word.endPos ?: 0
                        dur = (word.endPos ?: 0) - (word.begPos ?: 0)
                        dp_type = word.dpMessage ?: 0
                        
                        // 转换音素信息
                        val phones = mutableListOf<SpeechPhone>()
                        word.syllables.forEach { syllable ->
                            syllable.phones.forEach { phone ->
                                val speechPhone = SpeechPhone().apply {
                                    content = phone.content ?: ""
                                    score = phone.phoneTotalScore ?: 0f
                                    // 注意：SpeechPhone类中没有start、end、dur、gwpp字段
                                    // 这些字段在XML中存在但SpeechPhone类中没有对应字段
                                }
                                phones.add(speechPhone)
                            }
                        }
                        this.phone = phones
                    }
                    details.add(speechDetail)
                }
            }
            speechResult.details = details
            
            // 设置基本信息
            speechResult.res = readChapter.scorePattern ?: ""
            
            // 记录无法映射的字段
            recordSkippedFields(readChapter)
        }
        
        return speechResult
    }
    
    /**
     * 记录无法映射的字段
     */
    private fun recordSkippedFields(readChapter: ReadChapterDetail) {
        // SpeechResult中有但XML中没有的字段
        skippedFields.addAll(listOf(
            "systime", "pretime", "delaytime", "precision", "rank", 
            "wavetime", "pause", "speed", "audioUrl", "userMp3",
            "statics", "rhythm", "paragraphDetails", "info"
        ))
        
        // XML中有但SpeechResult中没有的字段
        skippedFields.addAll(listOf(
            "begPos", "endPos", "exceptInfo", "isRejected", "rejectType",
            "wordsPerMin", "syllables", "phones"
        ))
    }
    
    /**
     * 直接解析XML字符串并转换为SpeechResult
     * @param xmlString XML字符串内容
     * @return 转换后的SpeechResult
     */
    fun parseAndConvert(xmlString: String): SpeechResult {
        val voiceResult = parse(xmlString)
        return convertToSpeechResult(voiceResult)
    }

    /**
     * 获取无法映射的字段列表
     */
    fun getSkippedFields(): Set<String> = skippedFields.toSet()

    /**
     * 打印无法映射的字段信息
     */
    fun printSkippedFields() {
        println("=== 无法映射的字段汇总 ===")
        println("SpeechResult中有但XML中没有的字段:")
        val speechOnlyFields = listOf(
            "systime", "pretime", "delaytime", "precision", "rank", 
            "wavetime", "pause", "speed", "audioUrl", "userMp3",
            "statics", "rhythm", "paragraphDetails", "info"
        )
        speechOnlyFields.forEach { println("  - $it") }
        
        println("\nXML中有但SpeechResult中没有的字段:")
        val xmlOnlyFields = listOf(
            "begPos", "endPos", "exceptInfo", "isRejected", "rejectType",
            "wordsPerMin", "syllables", "phones", "syllAccent", "syllScore",
            "serrMsg", "gwpp", "dpMessage", "property", "globalIndex"
        )
        xmlOnlyFields.forEach { println("  - $it") }
        
        println("\n部分字段映射说明:")
        println("  - XML中的totalScore -> SpeechResult.overall")
        println("  - XML中的accuracyScore -> SpeechResult.accuracy") 
        println("  - XML中的integrityScore -> SpeechResult.integrity")
        println("  - XML中的fluencyScore -> SpeechResult.fluency.overall")
        println("  - XML中的word.totalScore -> SpeechDetails.score")
        println("  - XML中的phone.phoneTotalScore -> SpeechPhone.score")
    }
}

/**
 * 语音评测结果根对象
 */
data class VoiceEvaluationResult(
    var readChapter: ReadChapter? = null
)

/**
 * 阅读章节信息
 */
data class ReadChapter(
    var language: String? = null,      // 语言
    var type: String? = null,          // 类型
    var version: String? = null,       // 版本
    var recPaper: RecPaper? = null     // 录音试卷
)

/**
 * 录音试卷
 */
data class RecPaper(
    var readChapter: ReadChapterDetail? = null
)

/**
 * 阅读章节详细信息
 */
data class ReadChapterDetail(
    var accuracyScore: Float? = null,      // 准确度分数
    var begPos: Int? = null,               // 开始位置
    var content: String? = null,           // 内容文本
    var endPos: Int? = null,               // 结束位置
    var exceptInfo: Int? = null,           // 异常信息
    var fluencyScore: Float? = null,       // 流利度分数
    var integrityScore: Float? = null,     // 完整性分数
    var isRejected: Boolean = false,       // 是否拒评
    var rejectType: Int? = null,           // 拒评类型
    var scorePattern: String? = null,      // 评分模式
    var standardScore: Float? = null,      // 标准分数
    var totalScore: Float? = null,         // 总分
    var wordCount: Int? = null,            // 单词数量
    var wordsPerMin: Float? = null,        // 每分钟单词数
    var sentences: MutableList<Sentence> = mutableListOf()  // 句子列表
)

/**
 * 句子信息
 */
data class Sentence(
    var accuracyScore: Float? = null,      // 准确度分数
    var begPos: Int? = null,               // 开始位置
    var content: String? = null,           // 内容文本
    var endPos: Int? = null,               // 结束位置
    var fluencyScore: Float? = null,       // 流利度分数
    var index: Int? = null,                // 索引
    var standardScore: Float? = null,      // 标准分数
    var totalScore: Float? = null,         // 总分
    var wordCount: Int? = null,            // 单词数量
    var words: MutableList<Word> = mutableListOf()          // 单词列表
)

/**
 * 单词信息
 */
data class Word(
    var begPos: Int? = null,               // 开始位置
    var content: String? = null,           // 内容文本
    var dpMessage: Int? = null,            // 数据处理消息
    var endPos: Int? = null,               // 结束位置
    var globalIndex: Int? = null,          // 全局索引
    var index: Int? = null,                // 索引
    var property: Int? = null,             // 属性
    var totalScore: Float? = null,         // 总分
    var syllables: MutableList<Syllable> = mutableListOf()  // 音节列表
)

/**
 * 音节信息
 */
data class Syllable(
    var begPos: Int? = null,               // 开始位置
    var content: String? = null,           // 内容文本
    var endPos: Int? = null,               // 结束位置
    var serrMsg: Int? = null,              // 错误消息
    var syllAccent: Int? = null,           // 音节重音
    var syllScore: Float? = null,          // 音节分数
    var phones: MutableList<Phone> = mutableListOf()        // 音素列表
)

/**
 * 音素信息
 */
data class Phone(
    var begPos: Int? = null,               // 开始位置
    var content: String? = null,           // 内容文本
    var endPos: Int? = null,               // 结束位置
    var gwpp: Float? = null,               // GWPP分数
    var phoneTotalScore: Float? = null     // 音素总分
)

/*
使用示例:

val parser = VoiceEvaluationParser()

// 方法1: 分步解析和转换
val voiceResult = parser.parse(xmlString)
val speechResult = parser.convertToSpeechResult(voiceResult)

// 方法2: 直接解析并转换
val speechResult = parser.parseAndConvert(xmlString)

// 查看无法映射的字段
parser.printSkippedFields()

// 获取跳过的字段列表
val skippedFields = parser.getSkippedFields()
*/ 