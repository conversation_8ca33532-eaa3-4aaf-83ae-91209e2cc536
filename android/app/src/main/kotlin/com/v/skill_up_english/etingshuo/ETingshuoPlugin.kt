package com.v.skill_up_english.etingshuo

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.util.Log
import com.alibaba.fastjson.JSON
import com.v.skill_up_english.MainActivity
import com.iflytek.edu.ai.audio.common.EDUAIAudioAbilityBean
import com.iflytek.edu.ai.audio.evaluate.bean.EDUAIEvaluateBean
import com.iflytek.edu.ai.demo.EvalManager
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileOutputStream
import java.util.concurrent.atomic.AtomicBoolean


class ETingshuoPlugin(
    private val activity: Activity,
) : MethodChannel.MethodCallHandler {

    private val scope = CoroutineScope(Dispatchers.Main)



    companion object{
        var TAG:String="EtingshuoPlugin"

        fun registerWith(flutterEngine: FlutterEngine, context: Activity) {
            val channel = MethodChannel(
                flutterEngine.dartExecutor,
                "${MainActivity.pluginPre}${TAG}"
            )
            channel.setMethodCallHandler(
                ETingshuoPlugin(context)
            )
        }
    }

    private var audioRecord: AudioRecord? = null
    private var recordingThread: Thread? = null
    private val isRecording = AtomicBoolean(false)
    private val frameSize = 1280 // 每帧1280字节
    private var pcmFile: File? = null

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
         //kotlin
        when (call.method) {
            "initAIGlobal" -> {
                val userId=call.argument<String>("userId")?:""
                val appId=call.argument<String>("appId")?:""
//            userId: String, appId: String, dvId: String,userTag: String,logEnable: Boolean = false
                val dvId=call.argument<String>("dvId")?:""
                val userTag=call.argument<String>("userTag")?:""
                val logEnable=call.argument<Boolean>("logEnable")?:false
                val test=call.argument<Boolean>("test")?:false
                val token=call.argument<String>("token")?:""
                //初始化插件
                scope.launch {
                    val state= EvalManager.init(activity.application,token,userId,appId,dvId,userTag,logEnable,test)
                    result.success(state)
                }
            }
            "startEvaluator" ->{
                startRecording(call,result)
            }
            "stopRecord" ->{
                try {
                    stopRecording()
                    result.success(true)
                } catch (e: Exception) {
                    Log.d(TAG,"=============stopRecord ${e.message}")
                    result.success(false)
                }

            }
            else -> result.notImplemented()
        }
    }



    @SuppressLint("MissingPermission")
    private fun startRecording(call: MethodCall, result: MethodChannel.Result) {
//
//        mlanguage:String,
//        mpaper:String,
//        category:String,
//        group:String
        val mlanguage=call.argument<String>("language")?:""
        val mpaper=call.argument<String>("paper")?:""
        val category=call.argument<String>("category")?:""
        val group=call.argument<String>("group")?:""
        val recordPath=call.argument<String>("recordPath")
        val maxRecordMillSecondStr = call.argument<String>("maxRecordMillSecond") ?: (3000 * 60).toString()

        val maxRecordMillSecond=maxRecordMillSecondStr.toLong()

        if(isRecording.get()){
            Log.d(TAG,"================ isRecording.get()")
            stopRecording()
            result.success(false)
        }

        val sampleRate = 16000 // 16kHz采样率
        val channelConfig = AudioFormat.CHANNEL_IN_MONO
        val audioFormat = AudioFormat.ENCODING_PCM_16BIT
        audioRecord = AudioRecord(
            MediaRecorder.AudioSource.MIC,
            sampleRate,
            channelConfig,
            audioFormat,
            frameSize
        )
        if(recordPath?.isNotEmpty() ?: false){
            val file = File(recordPath)
            if (!file.exists()) {
                file.createNewFile()
            }
            pcmFile=file
        }else{
            pcmFile = File(activity.externalCacheDir, "audiorecord.pcm")
        }

        isRecording.set(true)
        audioRecord?.startRecording()
        recordingThread = Thread {
            writeAudioDataToFile()
        }
        recordingThread?.start()

        // 根据自己业务使用协程域
        Log.d(TAG,"=============start to execute evaluate");
        scope.launch {
            val evaluateBean = createEvaluateBean(true, activity.application,mlanguage,mpaper,category,group,maxRecordMillSecond)
            val startResult = EvalManager.startEvaluate(evaluateBean)
            if(startResult.engineError==null){
               result.success(JSON.toJSONString(startResult))
            }else{
                result.success(false)
                Log.d(TAG,"=============${startResult.engineError!!.code} -- ${startResult.engineError!!.msg}");
            }
        }
    }


    private fun writeAudioDataToFile() {
        val buffer = ByteArray(frameSize)
        FileOutputStream(pcmFile).use { os ->
            while (isRecording.get()) {
                val read = audioRecord?.read(buffer, 0, frameSize) ?: 0
                if (read > 0) {
                    // 这里可以对每帧1280字节的PCM数据进行自定义处理
                    os.write(buffer, 0, read)
//                    EvalManager.send(speexBuffer)
                }
            }
        }
    }

    private fun stopRecording() {
        isRecording.set(false)
        audioRecord?.apply {
            stop()
            release()
        }
        audioRecord = null
        recordingThread?.join()
        recordingThread = null
        EvalManager.stop()
    }

    // 创建评测参数
    private fun createEvaluateBean(
        isSpeex: Boolean,
        context: Application,
        mlanguage:String,
        mpaper:String,
        category:String,
        group:String,
        //毫秒
        maxRecordMillSecond:Long,
    ): EDUAIEvaluateBean {
        val audioParams = EDUAIAudioAbilityBean().apply {
            audioFileName = "test"
            audioFileFolder = context.cacheDir.absolutePath
            audioMaxRecordTime = maxRecordMillSecond
            audioRecordFormat = "speex"
        }

        Log.d(TAG,"======startRecording $mlanguage $mpaper $category $group ")

        return EDUAIEvaluateBean().apply {
            audioAbilityConfig = audioParams
            language =  mlanguage
            isPsc = false
            topicType = 0
            isEncrypt = true
            aioeFlag = 0
            transFormat = if (isSpeex) "speex" else "pcm" // 编码格式
            paper = mpaper
            engineParam = mutableMapOf(
//                "category" to "read_sentence",
//                "group" to "pupil", // 人群
                "parsePaper" to "true",
                "audioStorage" to "false", // 是否存储音频结果（文本评测不支持）
                "resultStorage" to "false" // 是否存储评测结果（文本评测不支持）
            )
            engineParam["region"] = "beijing" // 选填,地区
            engineParam["category"] = category // 选填,地区
            engineParam["group"] = group // 选填,地区
        }
    }

}