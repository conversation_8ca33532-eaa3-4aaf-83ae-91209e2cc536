plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}


android {
    namespace = "com.v.skill_up_english"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.v.skill_up_english"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdkVersion 24
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("debug")
        }
    }

    //执行lint检查，有任何的错误或者警告提示，都会终止构建，我们可以将其关掉。
    lintOptions {
        abortOnError false
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar','*.aar'])
    // implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'com.android.support:multidex:1.0.3'
    // implementation(libs.google.gson) 变为:
    implementation 'com.google.code.gson:gson:2.9.0'

    // implementation(libs.okhttp) 变为:
    implementation 'com.squareup.okhttp3:okhttp:4.6.0'

    // implementation(libs.protobuf) 变为:
    // 对于 Android 项目，通常使用 protobuf-javalite
    implementation 'com.google.protobuf:protobuf-java:3.21.7'
    implementation 'com.google.protobuf:protoc:3.21.7'


    //JSON解析
    implementation("com.alibaba:fastjson:1.2.79")
}