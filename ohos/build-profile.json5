{
  "app": {
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "certpath": "/Users/<USER>/.ohos/config/default_ohos_Pw6P8_4Ji4mwlUKJGtlbzXICqpncu4DaHym8U8toG0c=.cer",
          "keyAlias": "debugKey",
          "keyPassword": "0000001A3BCC3A1DDE9488FF84E1DBA0F034B79553D61E37C581EB6570E5E36423CD0E31FA688A83F0AF",
          "profile": "/Users/<USER>/.ohos/config/default_ohos_Pw6P8_4Ji4mwlUKJGtlbzXICqpncu4DaHym8U8toG0c=.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "/Users/<USER>/.ohos/config/default_ohos_Pw6P8_4Ji4mwlUKJGtlbzXICqpncu4DaHym8U8toG0c=.p12",
          "storePassword": "0000001A72CBD3C7D9F22406EE978869B6E1D11C86D1238E6A1C7EDE6816DB9DBC7D3110CE94AAD60A41"
        }
      }
    ],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.0.5(17)",
        "runtimeOS": "HarmonyOS",
      }
    ],
    "buildModeSet": [
      {
        "name": "debug"
      },
      {
        "name": "profile"
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    }
  ]
}