import 'dart:io';

import 'package:lib_base/config/storage_manager.dart';
import 'package:lib_base/config/utils/ui_util.dart';
import 'package:lib_base/resource/shared_preferences_keys.dart';
import 'package:skill_up_english/model/dic_item_model.dart';
import 'package:skill_up_english/model/speech_evaluating_result_info.dart';
import 'package:skill_up_english/utils/file_util.dart';

class DubbingUtil {
  static List<String> getWordtoneList() {
    var list = StorageManager.sharedPreferences
        .getJsonList(SharedPreferencesKeys.wordtonelist);
    List<String> result = [];
    if (list != null) {
      list.forEach((element) {
        DicItemModel model = DicItemModel.fromJson(element);
        if (model.word != null) {
          result.add(model.word!);
        }
      });
    }
    if (result.isEmpty) {
      result = specialWords;
    }
    return result;
  }

  static const List<String> specialWords = [
    "wow",
    "ha",
    "haha",
    "oops",
    "grr",
    "grrr",
    "grrrr",
    "sh",
    "shh",
    "brr",
    "rr",
    "hmm",
    "mmm",
    "mmmh",
    "ah",
    "ahh",
    "aaah",
    "aaahh",
    "aagghh",
    "uh",
    "huh",
    "ouch",
    "hooray",
    "ah-choo",
    "la",
    "dum-de-dum",
    "oooooh",
    "zzzz",
    "meow",
    "woof",
    "quack",
    "cluck",
    "moo",
    "neigh",
    "Whoops",
    "squeak",
    "umm",
    "aah",
    "aha",
    "aww",
    "ahem",
    "eww",
    "eh",
    "er",
    "ooh",
    "hum",
    "uh-huh",
    "aha",
    "whew",
    "noooo",
    "Pino"
  ];

  static SpeechEvaluationResult? handleSpeechEvaluationReslut(
    SpeechEvaluatingResultInfo info, {
    bool allowLowerScore = false,
  }) {
    List<Details>? details = info.result?.details;
    if (details != null && details.isNotEmpty) {
      SpeechEvaluationResult result = info.result!;
      List<String> wordLists = DubbingUtil.getWordtoneList();
      if (wordLists.isEmpty) {
        wordLists = specialWords;
      }
      //计算准确度
      num totalScore = 0;
      for (Details detail in details) {
        String content = detail.char ?? "";
        if (content.isNotEmpty) {
          content = content.toLowerCase();
          if (wordLists.contains(content)) {
            detail.score = 100;
          }
        }
        totalScore += detail.score ?? 0;
      }
      if ((result.accuracy ?? 0) == 0) {
        result.accuracy = totalScore / (details.length);
      }
      if (((result.overall ?? 0) < 20) && !allowLowerScore) {
        showToast("此句录音得分较低, 请重新录制");
        return null;
      }
      return result;
    }
  }

  static bool checkTone(String text) {
    if (text.isNotEmpty) {
      text = text
          .replaceAll(",", "")
          .replaceAll(".", "")
          .replaceAll("!", "")
          .replaceAll("?", "")
          .replaceAll(";", "")
          .replaceAll("...", "");
    }
    bool isContainsTone = false;
    List<String> toneWordList = getWordtoneList();
    List<String> wordLists;
    if (toneWordList.isEmpty) {
      wordLists = specialWords.toList();
    } else {
      wordLists = toneWordList;
    }
    if (wordLists.isNotEmpty && text.isNotEmpty) {
      if (text.contains(" ")) {
        List<String> evaArray = text.split(" ");
        for (String word in evaArray) {
          if (word.isNotEmpty) {
            isContainsTone = wordLists.contains(word.toLowerCase());
            if (isContainsTone) {
              break;
            }
          }
        }
      } else {
        isContainsTone = wordLists.contains(text.toLowerCase());
      }
    }
    return isContainsTone;
  }

  static double getActiveStar(num score, {num totalScore = 100}) {
    double activeStar = 0;
    if (score > 0) {
      if (score >= (0.9 * totalScore)) {
        activeStar = 3;
      } else if (score >= (0.8 * totalScore)) {
        activeStar = 2.5;
      } else if (score >= (0.7 * totalScore)) {
        activeStar = 2;
      } else if (score >= (0.6 * totalScore)) {
        activeStar = 1.5;
      } else {
        activeStar = 1;
      }
    }
    return activeStar;
  }

  //录音文件保存路径
  static Future<String> audioRecordFolder() async {
    String folder = await FileUtil.businessFolderPath();
    String folderPath = '$folder/webDubbing';
    var dic = Directory(folderPath);
    if (!dic.existsSync()) {
      dic.createSync(recursive: true);
    }
    return folderPath;
  }

  //录音文件路径
  static Future<String> audioRecordFile(String resourceId,
      {String pref = '.wav'}) async {
    String folderPath = await audioRecordFolder();
    return "$folderPath/${resourceId}$pref";
  }
}
