import 'dart:io';

import 'package:lib_base/config/storage_manager.dart';
import 'package:lib_base/log/log.dart';
import 'package:path_provider/path_provider.dart';

class FolderUtil {
  //通用的缓存文件夹
  static const commomTempFoler = 'temp';
}

class FileUtil {
  static Future<bool> copyTo(File origin, String target) async {
    if (origin.existsSync()) {
      origin.copySync(target);
      return true;
    } else {
      Logger.info("源文件 ${origin.path}不存在");
      return false;
    }
  }

  static String getFileSuffix(String filePath) {
    return filePath.split(".").last;
  }

  static Future<int> cacheSize() async {
    int totalSize = 0;
    String path = await eyybCachePath();
    int length = FileUtil.getDicLength(path);
    String documentCathPath = await eyybDocumentCachePath();
    int documentLength = getDicLength(documentCathPath);
    totalSize = length + documentLength;
    return totalSize;
  }

  static Future<String> eyybCachePath() async {
    var dir = await getApplicationCacheDirectory();
    String folderPath = '${dir.path}/eyyb';
    var dic = Directory(folderPath);
    if (!dic.existsSync()) {
      dic.createSync(recursive: true);
    }
    return folderPath;
  }

  static Future<String> eyybDocumentCachePath() async {
    var dir = await getApplicationDocumentsDirectory();
    String folderPath = '${dir.path}/eyyb';
    var dic = Directory(folderPath);
    if (!dic.existsSync()) {
      dic.createSync(recursive: true);
    }
    return folderPath;
  }

  static Future clearCache() async {
    StorageManager.clearLocalStorage();
    String cachPath = await FileUtil.eyybCachePath();
    Directory(cachPath).deleteSync(recursive: true);
    String documnetCachPath = await FileUtil.eyybDocumentCachePath();
    Directory(documnetCachPath).deleteSync(recursive: true);
    await Future.delayed(Duration(milliseconds: 100));
  }

  static int getDicLength(String dirPath) {
    int totalSize = 0;
    var dir = Directory(dirPath);
    try {
      if (dir.existsSync()) {
        dir
            .listSync(recursive: true, followLinks: false)
            .forEach((FileSystemEntity entity) {
          if (entity is File) {
            totalSize += entity.lengthSync();
          }
        });
      }
    } catch (e, stackTrace) {
      Logger.error(e.toString(), stackTrace);
    }

    return totalSize;
  }

  static String formatFileLength(int length) {
    double kb = length / 1024;
    double mb = kb / 1024;
    double gb = mb / 1024;
    if (gb >= 1) {
      return "${gb.toStringAsFixed(2)}GB";
    } else if (mb >= 1) {
      return "${mb.toStringAsFixed(2)}MB";
    } else {
      return "${kb.toStringAsFixed(2)}KB";
    }
  }

  //文件操作的根目录
  static Future<String> businessFolderPath(
      {String businessFolder = FolderUtil.commomTempFoler}) async {
    var dir = await getApplicationCacheDirectory();
    String folderPath =
        '${dir.path}/eyyb${(businessFolder.isNotEmpty) ? '/$businessFolder' : ''}';
    var dic = Directory(folderPath);
    if (!dic.existsSync()) {
      dic.createSync(recursive: true);
    }
    return folderPath;
  }
}
