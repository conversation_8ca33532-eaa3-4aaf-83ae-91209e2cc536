
import 'package:flutter/services.dart';
import 'package:lib_base/log/log.dart';
import 'package:skill_up_english/src/utils/asset_utils.dart';

class EtingshuoPhoneScoreItem{
  String? phonemeName;
  String? phoneticSymbol;
  String? phoneticRange;
  num? score;


  EtingshuoPhoneScoreItem({this.phonemeName, this.phoneticSymbol, this.phoneticRange, this.score});

  bool isMatch(num range){
     if(phoneticRange?.isNotEmpty??false){
      List<String> rangeList= phoneticRange!.split(",");
      if(rangeList.length==2){
        String startStr= rangeList[0];
        String endStr= rangeList[1];
        if(startStr.length>=2 && endStr.length>=2){
          String startFlag=startStr.substring(0,1);
          num? start=num.tryParse(startStr.substring(1));
          String endFlag=endStr.substring(endStr
          .length-1);
          num? end=num.tryParse(endStr.substring(0,endStr.length-1));
          if(start!=null && end !=null){
            Logger.info("=========================== startFlag:${startFlag}, endFlag:${endFlag}, startFlag==[:${startFlag=='['}, startFlag==(:${startFlag=='('}, endFlag==]:${endFlag==']'}, endFlag==):${endFlag==')'}");
              if(startFlag=='[' && endFlag=="]"){
                return range>= start && range<=end;
              }else if(startFlag=='[' && endFlag==")"){
                return range>= start && range<end;
              }else if(startFlag=="(" && endFlag=="]"){
                return range> start && range<=end;
              }else if(startFlag=="(" && endFlag==")"){
                return range> start && range<end;
              }else{
                return range> start && range<end;
              }
          }
        }
      }
     }
     Logger.error("============ ${phonemeName} 的 phoneticRange:${phoneticRange}格式不正确");
     return false;
  }

  @override
  String toString() {
    return 'EtingshuoPhoneScoreItem{phonemeName: $phonemeName, phoneticSymbol: $phoneticSymbol, phoneticRange: $phoneticRange, score: $score}';
  }
}

class SpeechEvaluationAssetUtil{

  static Map<String, List<EtingshuoPhoneScoreItem>> _etingshuoMap={};

  static String _getEtingshuoPhoneSymbolPath() {
    String path= "assets/speech_evaluation/etingshuo_phone_symbol.csv";
    return AssetsUtils.wrapAsset(path);
  }

  static String _getEtingshuoTbphonemescorerulePath() {
    String path= "assets/speech_evaluation/etingshuo_tbphonemescorerule.csv";
    return AssetsUtils.wrapAsset(path);
  }

  static Future<Map<String, List<EtingshuoPhoneScoreItem>>> _getEtingshuoTbphonemescorerules(String phoneContent) async {
    if(_etingshuoMap.isNotEmpty){
      return _etingshuoMap;
    }
    String csvData= await rootBundle.loadString(_getEtingshuoTbphonemescorerulePath(),cache: false);
    if(csvData.isNotEmpty){
      final List<String> lines = csvData.split('\n');
      for (int i = 1; i < lines.length; i++) {
        final String line = lines[i].trim();
        if (line.isEmpty) {
          continue;
        }
        // Split by one or more spaces
        final List<String> tokens = line.split(RegExp(r'\s+'));
        for (int j = 0; j < tokens.length; j += 4) {
          if (j + 3 < tokens.length) {
            // Ensure key and value are not empty after potential multiple spaces
            String phoneme_name = tokens[j].trim();
            String phonetic_symbol = tokens[j+1].trim();
            String phonetic_range = tokens[j+2].trim();
            String score = tokens[j+3].trim();
            if (phoneme_name.isNotEmpty) {
              List<EtingshuoPhoneScoreItem> list= _etingshuoMap.putIfAbsent(phoneme_name, () => []);
              list.add(EtingshuoPhoneScoreItem(phonemeName: phoneme_name,phoneticSymbol: phonetic_symbol,phoneticRange: phonetic_range,score: num.tryParse(score) ?? 0));
            }
          }
        }
      }
    }
    return _etingshuoMap;
  }

  static Future<EtingshuoPhoneScoreItem?> getEtingshuoTbphonemescore(String phoneContent,double score) async {


      final Map<String, List<EtingshuoPhoneScoreItem>> phoneSymbolMap = await _getEtingshuoTbphonemescorerules(phoneContent);

      List<EtingshuoPhoneScoreItem> tempList= phoneSymbolMap[phoneContent]??[];
      Logger.info("=================matched tempList:$tempList");
      for(EtingshuoPhoneScoreItem item in tempList){
        if(item.isMatch(score)){
          Logger.info("=================matched item:$item");
          return item;
        }
      }
    return null;
  }

  static Future<Map<String, String>> _getEtingshuoPhoneSymbolMap() async {
    final Map<String, String> phoneSymbolMap = {};
    try {
      final String csvData = await rootBundle.loadString(_getEtingshuoPhoneSymbolPath());
      if (csvData.isEmpty) {
        return phoneSymbolMap;
      }
      final List<String> lines = csvData.split('\n');

      // Skip the header line (index 0), process data lines starting from index 1
      for (int i = 1; i < lines.length; i++) {
        final String line = lines[i].trim();
        if (line.isEmpty) {
          continue;
        }
        // Split by one or more spaces
        final List<String> tokens = line.split(RegExp(r'\s+'));
        for (int j = 0; j < tokens.length; j += 2) {
          if (j + 1 < tokens.length) {
            // Ensure key and value are not empty after potential multiple spaces
            String key = tokens[j].trim();
            String value = tokens[j+1].trim();
            if (key.isNotEmpty) {
              phoneSymbolMap[key] = value;
            }
          }
        }
      }
    } catch (e,s) {
      // Optionally log the error
      // print('Error reading or parsing phone symbol CSV: $e');
      Logger.error("Error reading or parsing phone symbol CSV: $e",s);
    }
    return phoneSymbolMap;
  }

  static Future<String> getEtingshuoPhoneSymbol(String phoneContent) async {

      final Map<String, String> phoneSymbolMap = await _getEtingshuoPhoneSymbolMap();

      Logger.info("phoneSymbolMap:$phoneSymbolMap");
      return phoneSymbolMap[phoneContent] ?? "";

  }

}
