import 'package:lib_base/config/utils/ui_util.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:lib_base/ui/dialog/simple_alert_dialog.dart';

class PermissionRequestUtil {
  static Future<bool> hasMicrophonePermission() async {
    Permission permission = Permission.microphone;
    return permission.status.isGranted;
  }

  static Future<bool> requestMicrophonePermission(Function doSth) async {
    Permission permission = Permission.microphone;
    return requestPermission(
        permission: permission,
        doSth: doSth,
        title: "需要访问麦克风",
        desc: "录音或评测功能需要访问麦克风权限，请允许",
        permissionRequestStr: "录音或评测功能需要访问麦克风权限，请允许",);
  }

  static Future<bool> requestCamaraPermission(Function doSth) async {
    Permission permission = Permission.camera;
    return requestPermission(
        permission: permission,
        doSth: doSth,
        title: "需要访问相机",
        desc: "当前应用缺少相机权限，为了确保扫码功能正常使用，是否同意申请？",
        permissionRequestStr: "功能需要访问相机权限，请允许");
  }

  static Future<bool> requestPermission(
      {required Permission permission,
      required Function doSth,
      required String title,
      required String desc,
      required String permissionRequestStr}) async {
    //检测权限
    bool hasPermission = await permission.status.isGranted;
    if (hasPermission) {
      await doSth.call();
      return true;
    } else {
      //弹框申请权限
      await SimpleAlertDialog.showDialog(
          title: title,
          desc: desc,
          confirmText: "允许",
          confirm: () async {
            dismissDialog();
            bool hasPermission = await permission.request().isGranted;
            if (hasPermission) {
              await doSth.call();
            } else {
              showToast(permissionRequestStr);
            }
          },
          cancel: () {
            dismissDialog();
          });
      return false;
    }
  }
}
