import 'dart:async';
import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:lib_base/config/app_config.dart';
import 'package:lib_base/config/utils/ui_util.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/resource/method_channels.dart';
import 'package:skill_up_english/api/api_repository.dart';
import 'package:skill_up_english/config/MAppConfig.dart';
import 'package:skill_up_english/model/http/pt_list_phone_medata_xunfei_model.dart';
import 'package:skill_up_english/model/speech_evaluating_result_info.dart';
import 'package:skill_up_english/utils/permission_request_util.dart';
import 'package:skill_up_english/utils/speech_evaluation_asset_util.dart';
import 'package:xml/xml.dart';


enum EtingshuoLanguage { en, cn }

// • 适⽤⼈群⸺group，默认：adult
// ◦ adult 成⼈
// ◦ mhk 新疆
// ◦ youth 中学
// ◦ pupil ⼩学

enum EtingshuoGroup {
  adult,
  mhk,
  youth,
  pupil,
}

// 任务类型⸺category，默认： read_syllable
// ◦ read_syllable 朗读单字
// ◦ read_word 朗读词语
// ◦ read_sentence 朗读句⼦
// ◦ read_chapter 朗读篇章
// ◦ retell 复述
// ◦ simple_expression 情景反应
// ◦ topic 成⼈⼝头说话 /新疆话题表述
// ◦ topic_b 看图说话
// ◦ topic_c 命题说话
enum EtingshuoCategory {
  read_syllable,
  read_word,
  read_sentence,
  read_chapter,
  retell,
  simple_expression,
  topic,
  topic_b,
  topic_c,
}

//e听说  科大讯飞
class EtingshuoPlugin {
  //是否已经初始化
  static bool _hasInit = false;

  static const pluginName = '${CustomMethodChannels.pluginPre}EtingshuoPlugin';

  static String? _recordPath;

  static Future initSdk({
    required String userId,
    bool logEnable = false,
  }) async {
    var response = await BaseApiRepository.gettingshuoToken();
    if (response.isSuccess && response.isDataNotNull) {
      var token = response.dataNotNull.accessToken;
      if (token?.isNotEmpty ?? false) {
        //初始化
        var param = {
          "userId": userId,
          "appId": MAppConfig.etingshuoAppId,
          "dvId": AppConfig.deviceId,
          "userTag": MAppConfig.etingshuoUserTag,
          "logEnable": logEnable,
          "token": token,
        };
        Logger.info("initAIGlobal param:$param");
        return await MethodChannel(pluginName)
            .invokeMethod('initAIGlobal', param)
            .then((v) {
          Logger.info("========================== init ai global result:${v}");
          if (v == 1) {
            _hasInit = true;
          }
        });
      }
    }
//   val userId=call.argument<String>("userId")?:""
//                 val appId=call.argument<String>("appId")?:""
// //            userId: String, appId: String, dvId: String,userTag: String,logEnable: Boolean = false
//                 val dvId=call.argument<String>("dvId")?:""
//                 val userTag=call.argument<String>("userTag")?:""
//                 val logEnable=call.argument<Boolean>("logEnable")?:false
//                 val token=call.argument<String>("token")?:""
  }

  //paper 评测内容
  static Future startEnEvaluator({
    required String userId,
    required String paper,
    required EtingshuoCategory category,
    required EtingshuoGroup group,
    required EtingshuoLanguage language,
    required String recordPath,
    required int maxRecordMillSecond,
    bool audoStop = true,
    VoidCallback? onTimeOut,
  }) async {
    bool hasPermission =
    await PermissionRequestUtil.requestMicrophonePermission(() async {});
    if (hasPermission) {
      if (!_hasInit) {
        await initSdk(userId: userId, logEnable: true);
        await Future.delayed(Duration(milliseconds: 100));
      }
      if (audoStop) {
        Timer(Duration(milliseconds: maxRecordMillSecond), () {
          if (onTimeOut != null) {
            onTimeOut.call();
          } else {
            stopRecord();
          }
        });
      }
      var param = {
        "language": language.name,
        "paper": paper,
        "category": category.name,
        "group": group.name,
        'maxRecordMillSecond': (maxRecordMillSecond + 1000).toString(),
        "recordPath": recordPath,
      };
      _recordPath = recordPath;
      Logger.info("startEvaluator param:$param");
      return await MethodChannel(pluginName)
          .invokeMethod('startEvaluator', param)
          .then((v) async {
        Logger.info("==================== v.type:${v.runtimeType} v:${v}");
        if (v != null && v is String) {
          Map<String, dynamic> map = json.decode(v);
          if (map.containsKey("content")) {
            String content = map["content"];
            SpeechEvaluatingResultInfo speechEvaluatingResultInfo =
            SpeechEvaluatingResultInfo();
            speechEvaluatingResultInfo.refText = paper;
            speechEvaluatingResultInfo.requestId =
            map['statusBean']?['requestId'];
            SpeechEvaluationResult result =
            await _combileResult(content, category.name);

            speechEvaluatingResultInfo.result = result;
            return jsonDecode(jsonEncode(speechEvaluatingResultInfo));
          }else{
            Logger.error("==============评测失败:$v");
            showToast("评测失败，请重试");
          }
        }
        return v;
      });
    }
  }

  static Future<SpeechEvaluationResult> _combileResult(
      String content, String category) async {
    final document = XmlDocument.parse(content);
    //     this.overall,
    // this.fluency,
    // this.pron,
    // this.accuracy,
    // this.details,
    // this.integrity,
    SpeechEvaluationResult result = SpeechEvaluationResult();
    List<XmlElement> categorys =
        document.lastChild?.findAllElements(category).toList() ?? [];
    if (categorys.isNotEmpty) {
      XmlElement element = categorys.first;
      if (element.children.isNotEmpty) {
        List<XmlElement> chapters =
        element.findAllElements("read_chapter").toList();
        if (chapters.isNotEmpty) {
          XmlElement chapter = chapters.first;

          String? is_rejected = chapter.getAttribute("is_rejected");
          //为false 说明有效
          if (is_rejected == 'false') {
            List<String> contentList  =chapter.getAttribute("content")?.trim().split(" ")??[];

            result.overall =
                _parseSocreTo100(chapter.getAttribute("total_score"));
            result.fluency = Fluency(
                overall:
                _parseSocreTo100(chapter.getAttribute("fluency_score")));
            //todo  pron干嘛的
            result.accuracy =
                _parseSocreTo100(chapter.getAttribute("accuracy_score"));
            result.integrity =
                _parseSocreTo100(chapter.getAttribute("integrity_score"));
            //  details
            result.details = [];


            chapter.findAllElements("word").forEach((word) {
              Details details = Details();

              int? index = int.tryParse(word.getAttribute("index")??"");


              String? cchar = word.getAttribute("content");
              if(index!=null && contentList.length>index){
                cchar=contentList[index];
              }

              details.score =
                  _parseSocreTo100(word.getAttribute("total_score"));
              details.text = cchar;
              details.char=cchar;
              // Fluency? fluency;
              // List<Phone>? phone;
              // List<SntDetail>? snt_details;
              List<Phone> phones = [];

              word.findAllElements("phone").forEach((phone) async {
                Phone p = Phone();
                // String? ph2alpha;
                // String? char;
                // num? score;
                p.char = phone.getAttribute("content");
                double? gwpp= double.tryParse(phone.getAttribute("gwpp")??"");
                if((p.char?.isNotEmpty??false) && gwpp!=null){
                  EtingshuoPhoneScoreItem? item= await SpeechEvaluationAssetUtil.getEtingshuoTbphonemescore(p.char!, gwpp);
                  if(item!=null){
                    p.score=item.score;
                  }
                }

                phones.add(p);


              });
              details.phone = phones;
              result.details?.add(details);
            });
          } else {
            result.overall = 0;
            showToast("得分过低，请重试");
          }
        }
      }
    }
    return result;
  }

  static num _parseSocreTo100(String? value) {
    if (value != null) {
      return (double.tryParse(value) ?? 0) * 20;
    }
    return 0;
  }

  static Future stopRecord() async {
    Logger.info("========== start to stop record");
    try {
      await MethodChannel(pluginName).invokeMethod('stopRecord');
      Logger.info("==========  stop record 成功");
    } catch (e, s) {
      Logger.error("========== stop record 失败", s);
    }
  }

  static Future<String> getWavPath() async {
    return _recordPath ?? "";
  }
}
