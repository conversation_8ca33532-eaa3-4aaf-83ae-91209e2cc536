import 'dart:async';

import 'package:flutter/services.dart';
import 'package:lib_base/resource/method_channels.dart';
import 'package:skill_up_english/resource/plugins/speech_evaluation_sdk_plugin/etingshuo_plugin.dart';
import 'package:skill_up_english/utils/dubbing_util.dart';
import 'package:skill_up_english/utils/permission_request_util.dart';


//语音评测
class SpeechEvaluationSdkPlugin {



  static const pluginName =
      '${CustomMethodChannels.pluginPre}SingSoundSdkPlugin';

  static Future startEnEvaluator({
    String? refText,

    int timeoutSecond = 3,
    String? defaultCoreType,
    required String userId,
    EtingshuoGroup group=EtingshuoGroup.pupil,
    EtingshuoCategory category=EtingshuoCategory.read_sentence,
    bool audoStop = true,
    VoidCallback? onTimeOut,
  }) async {
    bool hasPermission =
    await PermissionRequestUtil.requestMicrophonePermission(() async {});
    if (hasPermission) {


           EtingshuoLanguage language=EtingshuoLanguage.en;
           String recordFilePath = await DubbingUtil.audioRecordFile(DateTime.now().millisecondsSinceEpoch.toString());
          return await EtingshuoPlugin.startEnEvaluator(userId: userId, paper: refText??"", category: category, group: group, language: language, recordPath: recordFilePath,maxRecordMillSecond: timeoutSecond*1000);

    }
  }

  static Future stopRecord() async {
    return await EtingshuoPlugin.stopRecord();
  }



  static Future<String> getWavPath() async {
    return EtingshuoPlugin.getWavPath();
  }

}
