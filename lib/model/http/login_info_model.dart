class LoginInfoModel {
  LoginInfoModel({
      this.studentInfo, 
      this.token,});

  LoginInfoModel.fromJson(dynamic json) {
    studentInfo = json['studentInfo'] != null ? StudentInfo.fromJson(json['studentInfo']) : null;
    token = json['token'];
  }
  StudentInfo? studentInfo;
  String? token;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (studentInfo != null) {
      map['studentInfo'] = studentInfo?.toJson();
    }
    map['token'] = token;
    return map;
  }

}

class StudentInfo {
  StudentInfo({
      this.id, 
      this.loginName, 
      this.shopId, 
      this.nickName, 
      this.sex, 
      this.phone, 
      this.photo, 
      this.age, 
      this.schoolId, 
      this.schoolName, 
      this.grade, 
      this.version, 
      this.teacherUserId, 
      this.teacherName, 
      this.serviceId, 
      this.serviceType, 
      this.coin,});

  StudentInfo.fromJson(dynamic json) {
    id = json['id'];
    loginName = json['loginName'];
    shopId = json['shopId'];
    nickName = json['nickName'];
    sex = json['sex'];
    phone = json['phone'];
    photo = json['photo'];
    age = json['age'];
    schoolId = json['schoolId'];
    schoolName = json['schoolName'];
    grade = json['grade'];
    version = json['version'];
    teacherUserId = json['teacherUserId'];
    teacherName = json['teacherName'];
    serviceId = json['serviceId'];
    serviceType = json['serviceType'];
    coin = json['coin'];
  }
  String? id;
  String? loginName;
  String? shopId;
  String? nickName;
  num? sex;
  String? phone;
  String? photo;
  num? age;
  String? schoolId;
  String? schoolName;
  String? grade;
  String? version;
  String? teacherUserId;
  String? teacherName;
  String? serviceId;
  // "serviceType":服务类型（0体验卡，1月卡，2季卡，3半年卡，4年卡）
  String? serviceType;
  String? coin;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['loginName'] = loginName;
    map['shopId'] = shopId;
    map['nickName'] = nickName;
    map['sex'] = sex;
    map['phone'] = phone;
    map['photo'] = photo;
    map['age'] = age;
    map['schoolId'] = schoolId;
    map['schoolName'] = schoolName;
    map['grade'] = grade;
    map['version'] = version;
    map['teacherUserId'] = teacherUserId;
    map['teacherName'] = teacherName;
    map['serviceId'] = serviceId;
    map['serviceType'] = serviceType;
    map['coin'] = coin;
    return map;
  }

}