


class PtListPhoneMedataXunfeiParam {
  String? name;
  int? phoneticRange;

  PtListPhoneMedataXunfeiParam({this.name, this.phoneticRange});

  PtListPhoneMedataXunfeiParam.fromJson(Map<String, dynamic> json) {
    if(json["name"] is String) {
      name = json["name"];
    }
    if(json["phoneticRange"] is int) {
      phoneticRange = json["phoneticRange"];
    }
  }

  static List<PtListPhoneMedataXunfeiParam> fromList(List<Map<String, dynamic>> list) {
    return list.map(PtListPhoneMedataXunfeiParam.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["name"] = name;
    _data["phoneticRange"] = phoneticRange;
    return _data;
  }
}

class PtListPhoneMedataXunfeiModel {
  String? phonemeName;
  String? phoneticSymbol;
  String? videoName;
  String? videoFile;
  String? frontImage;
  num? score;

  PtListPhoneMedataXunfeiModel({this.phonemeName, this.phoneticSymbol, this.videoName, this.videoFile, this.frontImage, this.score});

  PtListPhoneMedataXunfeiModel.fromJson(Map<String, dynamic> json) {
    if(json["phonemeName"] is String) {
      phonemeName = json["phonemeName"];
    }
    if(json["phoneticSymbol"] is String) {
      phoneticSymbol = json["phoneticSymbol"];
    }
    if(json["videoName"] is String) {
      videoName = json["videoName"];
    }
    if(json["videoFile"] is String) {
      videoFile = json["videoFile"];
    }
    if(json["frontImage"] is String) {
      frontImage = json["frontImage"];
    }
    if(json["score"] is num) {
      score = json["score"];
    }
  }

  static List<PtListPhoneMedataXunfeiModel> fromList(List<Map<String, dynamic>> list) {
    return list.map(PtListPhoneMedataXunfeiModel.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["phonemeName"] = phonemeName;
    _data["phoneticSymbol"] = phoneticSymbol;
    _data["videoName"] = videoName;
    _data["videoFile"] = videoFile;
    _data["frontImage"] = frontImage;
    _data["score"] = score;
    return _data;
  }
}