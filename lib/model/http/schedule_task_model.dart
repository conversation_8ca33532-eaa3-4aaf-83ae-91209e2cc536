class ScheduleTaskModel {
  ScheduleTaskModel({
      this.wordList, 
      this.taskConfigList,});

  ScheduleTaskModel.fromJson(dynamic json) {
    wordList = json['wordList'] != null ? json['wordList'].cast<String>() : [];
    if (json['taskConfigList'] != null) {
      taskConfigList = [];
      json['taskConfigList'].forEach((v) {
        taskConfigList?.add(TaskConfigList.fromJson(v));
      });
    }
  }
  List<String>? wordList;
  List<TaskConfigList>? taskConfigList;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['wordList'] = wordList;
    if (taskConfigList != null) {
      map['taskConfigList'] = taskConfigList?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

class TaskConfigList {
  TaskConfigList({
      this.taskScheduleId, 
      this.taskConfigDetailsId, 
      this.taskName, 
      this.taskType, 
      this.reward, 
      this.testQualified, 
      this.predictTimes, 
      this.actualTimes, 
      this.wordNum, 
      this.completedWordNum, 
      this.state,});

  TaskConfigList.fromJson(dynamic json) {
    taskScheduleId = json['taskScheduleId'];
    taskConfigDetailsId = json['taskConfigDetailsId'];
    taskName = json['taskName'];
    taskType = json['taskType'];
    reward = json['reward'];
    testQualified = json['testQualified'];
    predictTimes = json['predictTimes'];
    actualTimes = json['actualTimes'];
    wordNum = json['wordNum'];
    completedWordNum = json['completedWordNum'];
    state = json['state'];
  }
  String? taskScheduleId;
  String? taskConfigDetailsId;
  String? taskName;
  // 1,任务类型 1学习，2训练，3测试
  num? taskType;
  num? reward;
  num? testQualified;
  num? predictTimes;
  num? actualTimes;
  num? wordNum;
  num? completedWordNum;
  // -1过期，0未开始，1进行中，2结束
  num? state;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['taskScheduleId'] = taskScheduleId;
    map['taskConfigDetailsId'] = taskConfigDetailsId;
    map['taskName'] = taskName;
    map['taskType'] = taskType;
    map['reward'] = reward;
    map['testQualified'] = testQualified;
    map['predictTimes'] = predictTimes;
    map['actualTimes'] = actualTimes;
    map['wordNum'] = wordNum;
    map['completedWordNum'] = completedWordNum;
    map['state'] = state;
    return map;
  }

}