
class EtingshuoTokenModel {
  String? retCode;
  String? accessToken;
  String? expiresIn;

  EtingshuoTokenModel({this.retCode, this.accessToken, this.expiresIn});

  EtingshuoTokenModel.fromJson(Map<String, dynamic> json) {
    if(json["retCode"] is String) {
      retCode = json["retCode"];
    }
    if(json["accessToken"] is String) {
      accessToken = json["accessToken"];
    }
    if(json["expiresIn"] is String) {
      expiresIn = json["expiresIn"];
    }
  }

  static List<EtingshuoTokenModel> fromList(List<Map<String, dynamic>> list) {
    return list.map(EtingshuoTokenModel.fromJson).toList();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["retCode"] = retCode;
    _data["accessToken"] = accessToken;
    _data["expiresIn"] = expiresIn;
    return _data;
  }
}