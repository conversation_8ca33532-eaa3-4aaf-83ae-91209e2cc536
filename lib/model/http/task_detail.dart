class TaskDetail {
  TaskDetail({
      this.unitWordId, 
      this.wordId, 
      this.wordName, 
      this.taskWordDetailsId, 
      this.state, 
      this.predictTimes, 
      this.actualTimes, 
      this.taskConfigDetailsId, 
      this.wordDrillVO,});

  TaskDetail.fromJson(dynamic json) {
    unitWordId = json['unitWordId'];
    wordId = json['wordId'];
    wordName = json['wordName'];
    taskWordDetailsId = json['taskWordDetailsId'];
    state = json['state'];
    predictTimes = json['predictTimes'];
    actualTimes = json['actualTimes'];
    taskConfigDetailsId = json['taskConfigDetailsId'];
    wordDrillVO = json['wordDrillVO'] != null ? WordDrillVo.fromJson(json['wordDrillVO']) : null;
  }
  String? unitWordId;
  String? wordId;
  String? wordName;
  String? taskWordDetailsId;
  num? state;
  num? predictTimes;
  num? actualTimes;
  String? taskConfigDetailsId;
  WordDrillVo? wordDrillVO;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['unitWordId'] = unitWordId;
    map['wordId'] = wordId;
    map['wordName'] = wordName;
    map['taskWordDetailsId'] = taskWordDetailsId;
    map['state'] = state;
    map['predictTimes'] = predictTimes;
    map['actualTimes'] = actualTimes;
    map['taskConfigDetailsId'] = taskConfigDetailsId;
    if (wordDrillVO != null) {
      map['wordDrillVO'] = wordDrillVO?.toJson();
    }
    return map;
  }

}

class WordDrillVo {
  WordDrillVo({
      this.id, 
      this.moduleConfigId, 
      this.moduleUnitConfigId, 
      this.wordId, 
      this.wordName, 
      this.wordAttributeId, 
      this.wordAttributeName, 
      this.wordMeaningId, 
      this.wordMeaningName, 
      this.refId, 
      this.info, 
      this.identifyDisturb, 
      this.hearDisturb, 
      this.pictureDisturb, 
      this.expendList, 
      this.answerTime, 
      this.wordSoundmarkId, 
      this.soundmark, 
      this.syllables, 
      this.phonemeList,});

  WordDrillVo.fromJson(dynamic json) {
    id = json['id'];
    moduleConfigId = json['moduleConfigId'];
    moduleUnitConfigId = json['moduleUnitConfigId'];
    wordId = json['wordId'];
    wordName = json['wordName'];
    wordAttributeId = json['wordAttributeId'];
    wordAttributeName = json['wordAttributeName'];
    wordMeaningId = json['wordMeaningId'];
    wordMeaningName = json['wordMeaningName'];
    refId = json['refId'];
    info = json['info'] != null ? Info.fromJson(json['info']) : null;
    identifyDisturb = json['identifyDisturb'];
    hearDisturb = json['hearDisturb'];
    pictureDisturb = json['pictureDisturb'];
    if (json['expendList'] != null) {
      expendList = [];
      json['expendList'].forEach((v) {
        expendList?.add(ExpendList.fromJson(v));
      });
    }
    answerTime = json['answerTime'];
    wordSoundmarkId = json['wordSoundmarkId'];
    soundmark = json['soundmark'] != null ? Soundmark.fromJson(json['soundmark']) : null;
    if (json['syllables'] != null) {
      syllables = [];
      json['syllables'].forEach((v) {
        syllables?.add(Syllables.fromJson(v));
      });
    }
    if (json['phonemeList'] != null) {
      phonemeList = [];
      json['phonemeList'].forEach((v) {
        phonemeList?.add(PhonemeList.fromJson(v));
      });
    }
  }
  String? id;
  String? moduleConfigId;
  String? moduleUnitConfigId;
  String? wordId;
  String? wordName;
  String? wordAttributeId;
  String? wordAttributeName;
  String? wordMeaningId;
  String? wordMeaningName;
  String? refId;
  Info? info;
  String? identifyDisturb;
  String? hearDisturb;
  String? pictureDisturb;
  List<ExpendList>? expendList;
  num? answerTime;
  String? wordSoundmarkId;
  Soundmark? soundmark;
  List<Syllables>? syllables;
  List<PhonemeList>? phonemeList;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['moduleConfigId'] = moduleConfigId;
    map['moduleUnitConfigId'] = moduleUnitConfigId;
    map['wordId'] = wordId;
    map['wordName'] = wordName;
    map['wordAttributeId'] = wordAttributeId;
    map['wordAttributeName'] = wordAttributeName;
    map['wordMeaningId'] = wordMeaningId;
    map['wordMeaningName'] = wordMeaningName;
    map['refId'] = refId;
    if (info != null) {
      map['info'] = info?.toJson();
    }
    map['identifyDisturb'] = identifyDisturb;
    map['hearDisturb'] = hearDisturb;
    map['pictureDisturb'] = pictureDisturb;
    if (expendList != null) {
      map['expendList'] = expendList?.map((v) => v.toJson()).toList();
    }
    map['answerTime'] = answerTime;
    map['wordSoundmarkId'] = wordSoundmarkId;
    if (soundmark != null) {
      map['soundmark'] = soundmark?.toJson();
    }
    if (syllables != null) {
      map['syllables'] = syllables?.map((v) => v.toJson()).toList();
    }
    if (phonemeList != null) {
      map['phonemeList'] = phonemeList?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

class PhonemeList {
  PhonemeList({
      this.id, 
      this.wordId, 
      this.soundmarkId, 
      this.phonemeName, 
      this.phonemeId, 
      this.note, 
      this.audio, 
      this.sort,});

  PhonemeList.fromJson(dynamic json) {
    id = json['id'];
    wordId = json['wordId'];
    soundmarkId = json['soundmarkId'];
    phonemeName = json['phonemeName'];
    phonemeId = json['phonemeId'];
    note = json['note'];
    audio = json['audio'];
    sort = json['sort'];
  }
  String? id;
  dynamic wordId;
  String? soundmarkId;
  String? phonemeName;
  String? phonemeId;
  dynamic note;
  dynamic audio;
  dynamic sort;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['wordId'] = wordId;
    map['soundmarkId'] = soundmarkId;
    map['phonemeName'] = phonemeName;
    map['phonemeId'] = phonemeId;
    map['note'] = note;
    map['audio'] = audio;
    map['sort'] = sort;
    return map;
  }

}

class Syllables {
  Syllables({
      this.id, 
      this.wordId, 
      this.soundmarkId, 
      this.syllableName, 
      this.syllableAudio, 
      this.sort,});

  Syllables.fromJson(dynamic json) {
    id = json['id'];
    wordId = json['wordId'];
    soundmarkId = json['soundmarkId'];
    syllableName = json['syllableName'];
    syllableAudio = json['syllableAudio'];
    sort = json['sort'];
  }
  String? id;
  dynamic wordId;
  String? soundmarkId;
  String? syllableName;
  String? syllableAudio;
  dynamic sort;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['wordId'] = wordId;
    map['soundmarkId'] = soundmarkId;
    map['syllableName'] = syllableName;
    map['syllableAudio'] = syllableAudio;
    map['sort'] = sort;
    return map;
  }

}

class Soundmark {
  Soundmark({
      this.id, 
      this.wordId, 
      this.soundmark, 
      this.soundmarkType, 
      this.syllableSplit, 
      this.phonemeSplit, 
      this.soundmarkSplit, 
      this.vowel, 
      this.pronounce, 
      this.sort, 
      this.syllables, 
      this.phonemes,});

  Soundmark.fromJson(dynamic json) {
    id = json['id'];
    wordId = json['wordId'];
    soundmark = json['soundmark'];
    soundmarkType = json['soundmarkType'];
    syllableSplit = json['syllableSplit'];
    phonemeSplit = json['phonemeSplit'];
    soundmarkSplit = json['soundmarkSplit'];
    vowel = json['vowel'];
    pronounce = json['pronounce'];
    sort = json['sort'];
    syllables = json['syllables'];
    phonemes = json['phonemes'];
  }
  String? id;
  dynamic wordId;
  String? soundmark;
  String? soundmarkType;
  String? syllableSplit;
  String? phonemeSplit;
  String? soundmarkSplit;
  String? vowel;
  String? pronounce;
  dynamic sort;
  dynamic syllables;
  dynamic phonemes;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['wordId'] = wordId;
    map['soundmark'] = soundmark;
    map['soundmarkType'] = soundmarkType;
    map['syllableSplit'] = syllableSplit;
    map['phonemeSplit'] = phonemeSplit;
    map['soundmarkSplit'] = soundmarkSplit;
    map['vowel'] = vowel;
    map['pronounce'] = pronounce;
    map['sort'] = sort;
    map['syllables'] = syllables;
    map['phonemes'] = phonemes;
    return map;
  }

}

class ExpendList {
  ExpendList({
      this.id, 
      this.wordId, 
      this.type, 
      this.filePath, 
      this.content, 
      this.sort, 
      this.isTrue, 
      this.wordInfoList, 
      this.chineseList, 
      this.sentenceInfo, 
      this.refId,});

  ExpendList.fromJson(dynamic json) {
    id = json['id'];
    wordId = json['wordId'];
    type = json['type'];
    filePath = json['filePath'];
    content = json['content'];
    sort = json['sort'];
    isTrue = json['isTrue'];
    wordInfoList = json['wordInfoList'];
    chineseList = json['chineseList'];
    sentenceInfo = json['sentenceInfo'];
    refId = json['refId'];
  }
  String? id;
  dynamic wordId;
  dynamic type;
  String? filePath;
  dynamic content;
  dynamic sort;
  dynamic isTrue;
  dynamic wordInfoList;
  dynamic chineseList;
  dynamic sentenceInfo;
  dynamic refId;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['wordId'] = wordId;
    map['type'] = type;
    map['filePath'] = filePath;
    map['content'] = content;
    map['sort'] = sort;
    map['isTrue'] = isTrue;
    map['wordInfoList'] = wordInfoList;
    map['chineseList'] = chineseList;
    map['sentenceInfo'] = sentenceInfo;
    map['refId'] = refId;
    return map;
  }

}

class Info {
  Info({
      this.id, 
      this.remarks, 
      this.createDate, 
      this.updateDate, 
      this.content, 
      this.translate, 
      this.usages, 
      this.filePath, 
      this.createBy, 
      this.updateBy, 
      this.imagePath, 
      this.mp3Time, 
      this.synFlag, 
      this.pattern, 
      this.struct, 
      this.vedio, 
      this.imageGrid, 
      this.releaseState, 
      this.type, 
      this.ossPrefix, 
      this.analysis,});

  Info.fromJson(dynamic json) {
    id = json['id'];
    remarks = json['remarks'];
    createDate = json['createDate'];
    updateDate = json['updateDate'];
    content = json['content'];
    translate = json['translate'];
    usages = json['usages'];
    filePath = json['filePath'];
    createBy = json['createBy'];
    updateBy = json['updateBy'];
    imagePath = json['imagePath'];
    mp3Time = json['mp3Time'];
    synFlag = json['synFlag'];
    pattern = json['pattern'];
    struct = json['struct'];
    vedio = json['vedio'];
    imageGrid = json['imageGrid'];
    releaseState = json['releaseState'];
    type = json['type'];
    ossPrefix = json['ossPrefix'];
    analysis = json['analysis'];
  }
  String? id;
  dynamic remarks;
  dynamic createDate;
  dynamic updateDate;
  String? content;
  String? translate;
  dynamic usages;
  dynamic filePath;
  dynamic createBy;
  dynamic updateBy;
  dynamic imagePath;
  dynamic mp3Time;
  dynamic synFlag;
  dynamic pattern;
  dynamic struct;
  dynamic vedio;
  dynamic imageGrid;
  dynamic releaseState;
  dynamic type;
  dynamic ossPrefix;
  dynamic analysis;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['remarks'] = remarks;
    map['createDate'] = createDate;
    map['updateDate'] = updateDate;
    map['content'] = content;
    map['translate'] = translate;
    map['usages'] = usages;
    map['filePath'] = filePath;
    map['createBy'] = createBy;
    map['updateBy'] = updateBy;
    map['imagePath'] = imagePath;
    map['mp3Time'] = mp3Time;
    map['synFlag'] = synFlag;
    map['pattern'] = pattern;
    map['struct'] = struct;
    map['vedio'] = vedio;
    map['imageGrid'] = imageGrid;
    map['releaseState'] = releaseState;
    map['type'] = type;
    map['ossPrefix'] = ossPrefix;
    map['analysis'] = analysis;
    return map;
  }

}