
import 'package:skill_up_english/model/speech_evaluating_result_info.dart';

class SpeechEvaluatingResultModel {
  SpeechEvaluationResult? singsoundResult;
  //内容
  String? content;

  //星星数目
  double? starCount;



  SpeechEvaluatingResultModel(
      {this.singsoundResult,
      this.content,
      this.starCount,
   }) {

  }

  int? getMillseconds(String? pointStr) {
    if (pointStr != null) {
      double? seconds = double.tryParse(pointStr);
      if (seconds != null) {
        int millseconds = (seconds * 1000).toInt();
        return millseconds;
      }
    }
    return null;
  }
}

// 测评结果;
class SoundEvaluatingResultModel {
  String? code;
  String? score;
  String? file;
  String? error;
  SoundEvaluatingResultModel({this.code, this.score, this.file, this.error}) {}
}
