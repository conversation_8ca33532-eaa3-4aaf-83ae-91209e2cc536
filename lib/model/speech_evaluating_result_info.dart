/// request_id : "0e86b7668d3311efbd46b7a9ec3f6b9e"
/// applicationId : "a286"
/// dtLastResponse : "2024-10-18 17:26:31:120"
/// connect : {"param":{"app":{"signature":"006f066563d451495cfcbed0d4158915ffebbea1","userId":"LkDmdL5ZkQvDDsiqUKfXZG","applicationId":"a286","connect_id":"0e8fe07a8d3311ef8a7ee3a95023ed05","deviceId":"b3258ad2-d363-4a6a-a68d-56e475ca960a","warrantId":"671245e5917a9a342ecd67cc669daf30","timestamp":"1729243589"},"sdk":{"os":"linux","product":"fake","os_version":"0.0","source":6,"protocol":1,"type":1,"arch":"aarch64","version":50332416}},"cmd":"connect"}
/// params : {"app":{"signature":"006f066563d451495cfcbed0d4158915ffebbea1","userId":"LkDmdL5ZkQvDDsiqUKfXZG","applicationId":"a286","connect_id":"0e8fe07a8d3311ef8a7ee3a95023ed05","deviceId":"b3258ad2-d363-4a6a-a68d-56e475ca960a","warrantId":"671245e5917a9a342ecd67cc669daf30","timestamp":"1729243589"},"audio":{"saveAudio":0,"sampleBytes":2,"audioType":"ogg","sampleRate":16000,"channel":1},"request":{"request_id":"0e86b7668d3311efbd46b7a9ec3f6b9e","phdet":1,"precision":1,"attachAudioUrl":0,"outputPhones":1,"feedback":0,"refText":"Catch the ball!","symbol":1,"coreType":"en.sent.score","rank":100}}
/// recordId : "11ef8d330d816d4691e4a00028601020600000005522f"
/// refText : "Catch the ball!"
/// eof : 1
/// result : {"overall":93,"forceout":0,"precision":1,"systime":2004,"res":"eng.snt.online.1.0","rank":100,"rhythm":{"stress":67,"overall":92,"tone":100,"sense":100},"fluency":{"pause":0,"overall":92,"speed":1},"pron":94,"wavetime":2530,"accuracy":94,"details":[{"stressref":0,"tonescore":0,"dur":390,"char":"Catch","liaisonref":0,"liaisonscore":0,"senseref":0,"start":390,"fluency":95,"score":91,"stressscore":0,"toneref":0,"sensescore":0,"phone":[{"phid":"1","ph2alpha":"c","start":390,"char":"k","score":86,"end":540,"pherr":0},{"phid":"2","ph2alpha":"a","start":540,"char":"ae","score":89,"end":600,"pherr":0},{"phid":"3_4_5","ph2alpha":"tch","start":600,"char":"ch","score":97,"end":780,"pherr":0}],"end":780,"accent":"ea"},{"stressref":0,"tonescore":0,"dur":90,"char":"the","liaisonref":0,"liaisonscore":0,"senseref":0,"start":780,"fluency":89,"score":96,"stressscore":0,"toneref":0,"sensescore":0,"phone":[{"phid":"1_2","ph2alpha":"th","start":780,"char":"dh","score":94,"end":810,"pherr":0},{"phid":"3","ph2alpha":"e","start":810,"char":"ax","score":99,"end":870,"pherr":0}],"end":870,"accent":"ea"},{"stressref":0,"tonescore":1,"dur":660,"char":"ball!","liaisonref":0,"liaisonscore":0,"senseref":1,"start":870,"fluency":91,"score":94,"stressscore":1,"toneref":0,"sensescore":1,"phone":[{"phid":"1","ph2alpha":"b","start":870,"char":"b","score":89,"end":990,"pherr":0},{"phid":"2","ph2alpha":"a","start":990,"char":"ao","score":97,"end":1110,"pherr":0},{"phid":"3_4","ph2alpha":"ll","start":1110,"char":"l","score":96,"end":1530,"pherr":0}],"end":1530,"accent":"ea"}],"info":{"tipId":0,"clip":0,"snr":26.379105,"volume":98},"statics":[{"score":86,"char":"k","count":1},{"score":89,"char":"ae","count":1},{"score":97,"char":"ch","count":1},{"score":94,"char":"dh","count":1},{"score":99,"char":"ax","count":1},{"score":89,"char":"b","count":1},{"score":97,"char":"ao","count":1},{"score":96,"char":"l","count":1}],"delaytime":14,"integrity":100,"pretime":1,"version":"0.0.80.2024.1.14.09:46:45"}
/// cloud_platform : {"origin_audio_length":9859}

class SpeechEvaluatingResultInfo {
  SpeechEvaluatingResultInfo({
    this.requestId,
    this.refText,
    this.result,
  });

  SpeechEvaluatingResultInfo.fromJson(dynamic json) {
    requestId = json['request_id'];
    refText = json['refText'];
    result = json['result'] != null
        ? SpeechEvaluationResult.fromJson(json['result'])
        : null;
  }
  String? requestId;    
  String? refText;
  
  SpeechEvaluationResult? result;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['request_id'] = requestId; 
    map['refText'] = refText;
    if (result != null) {
      map['result'] = result?.toJson();
    }
    return map;
  }
}

/// origin_audio_length : 9859

 
/// overall : 93
/// forceout : 0
/// precision : 1
/// systime : 2004
/// res : "eng.snt.online.1.0"
/// rank : 100
/// rhythm : {"stress":67,"overall":92,"tone":100,"sense":100}
/// fluency : {"pause":0,"overall":92,"speed":1}
/// pron : 94
/// wavetime : 2530
/// accuracy : 94
/// details : [{"stressref":0,"tonescore":0,"dur":390,"char":"Catch","liaisonref":0,"liaisonscore":0,"senseref":0,"start":390,"fluency":95,"score":91,"stressscore":0,"toneref":0,"sensescore":0,"phone":[{"phid":"1","ph2alpha":"c","start":390,"char":"k","score":86,"end":540,"pherr":0},{"phid":"2","ph2alpha":"a","start":540,"char":"ae","score":89,"end":600,"pherr":0},{"phid":"3_4_5","ph2alpha":"tch","start":600,"char":"ch","score":97,"end":780,"pherr":0}],"end":780,"accent":"ea"},{"stressref":0,"tonescore":0,"dur":90,"char":"the","liaisonref":0,"liaisonscore":0,"senseref":0,"start":780,"fluency":89,"score":96,"stressscore":0,"toneref":0,"sensescore":0,"phone":[{"phid":"1_2","ph2alpha":"th","start":780,"char":"dh","score":94,"end":810,"pherr":0},{"phid":"3","ph2alpha":"e","start":810,"char":"ax","score":99,"end":870,"pherr":0}],"end":870,"accent":"ea"},{"stressref":0,"tonescore":1,"dur":660,"char":"ball!","liaisonref":0,"liaisonscore":0,"senseref":1,"start":870,"fluency":91,"score":94,"stressscore":1,"toneref":0,"sensescore":1,"phone":[{"phid":"1","ph2alpha":"b","start":870,"char":"b","score":89,"end":990,"pherr":0},{"phid":"2","ph2alpha":"a","start":990,"char":"ao","score":97,"end":1110,"pherr":0},{"phid":"3_4","ph2alpha":"ll","start":1110,"char":"l","score":96,"end":1530,"pherr":0}],"end":1530,"accent":"ea"}]
/// info : {"tipId":0,"clip":0,"snr":26.379105,"volume":98}
/// statics : [{"score":86,"char":"k","count":1},{"score":89,"char":"ae","count":1},{"score":97,"char":"ch","count":1},{"score":94,"char":"dh","count":1},{"score":99,"char":"ax","count":1},{"score":89,"char":"b","count":1},{"score":97,"char":"ao","count":1},{"score":96,"char":"l","count":1}]
/// delaytime : 14
/// integrity : 100
/// pretime : 1
/// version : "0.0.80.2024.1.14.09:46:45"

class SpeechEvaluationResult {
  SpeechEvaluationResult({
    this.overall,
    // this.forceout,
    // this.precision,
    // this.systime,
    // this.res,
    // this.rank,
    // this.rhythm,
    this.fluency,
    this.pron,
    // this.wavetime,
    this.accuracy,
    this.details,
    // this.info,
    // this.statics,
    // this.delaytime,
    this.integrity,
    // this.pretime,
    // this.version,
    // this.snt_details,
  });

  SpeechEvaluationResult.fromJson(dynamic json) {
    overall = json['overall'];
    // forceout = json['forceout'];
    // precision = json['precision'];
    // systime = json['systime'];
    // res = json['res'];
    // rank = json['rank'];
    // rhythm = json['rhythm'] != null
    //     ? json['rhythm'] is Map<String, dynamic>
    //         ? Rhythm.fromJson(json['rhythm'])
    //         : Rhythm(overall: (json['rhythm'] is num) ? json['rhythm'] : 0)
    //     : null;
    fluency = null;
    if (json['fluency'] != null) {
      if (json['fluency'] is Map<String, dynamic> ||
          json['fluency'] is Map<Object?, Object?>) {
        fluency = Fluency.fromJson(json['fluency']);
      } else {
        fluency =
            Fluency(overall: (json['fluency'] is num) ? json['fluency'] : 0);
      }
    }
    // fluency = json['fluency'] != null
    //     ? (json['fluency'] is Map<String, dynamic> ||
    //             json['fluency'] is Map<Object?, Object?>)
    //         ? Fluency.fromJson(json['fluency'])
    //         : Fluency(overall: (json['fluency'] is num) ? json['fluency'] : 0)
    //     : null;
    pron = json['pron'];
    // wavetime = json['wavetime'];
    accuracy = json['accuracy'];
    if (json['details'] != null) {
      details = [];
      json['details'].forEach((v) {
        details?.add(Details.fromJson(v));
      });
    }
    // info = json['info'] != null ? Info.fromJson(json['info']) : null;
    // if (json['statics'] != null) {
    //   statics = [];
    //   json['statics'].forEach((v) {
    //     statics?.add(Statics.fromJson(v));
    //   });
    // }
    // delaytime = json['delaytime'];
    integrity = json['integrity'];
    // pretime = json['pretime'];
    // version = json['version'];
    // snt_details = json['snt_details'];
  }
  num? overall;
  // num? forceout;
  // num? precision;
  // num? systime;
  // String? res;
  // num? rank;

  // Rhythm? rhythm;
  Fluency? fluency;
  num? pron;
  // num? wavetime;
  num? accuracy;
  List<Details>? details;
  // Info? info;
  // List<Statics>? statics;
  num? integrity;
  // List<SntDetail>? snt_details;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['overall'] = overall;
    // map['forceout'] = forceout;
    // map['precision'] = precision;
    // map['systime'] = systime;
    // map['res'] = res;
    // map['rank'] = rank;
    // if (rhythm != null) {
    //   map['rhythm'] = rhythm;
    // }
    if (fluency != null) {
      map['fluency'] = fluency?.toJson();
    }
    map['pron'] = pron;
    // map['wavetime'] = wavetime;
    map['accuracy'] = accuracy;
    if (details != null) {
      map['details'] = details?.map((v) => v.toJson()).toList();
    }
    // if (info != null) {
    //   map['info'] = info?.toJson();
    // }
    // if (statics != null) {
    //   map['statics'] = statics?.map((v) => v.toJson()).toList();
    // }
    // map['delaytime'] = delaytime;
    map['integrity'] = integrity;
    // map['pretime'] = pretime;
    // map['version'] = version;
    // map['snt_details'] = snt_details;
    return map;
  }
}

/// score : 86
/// char : "k"
/// count : 1

class Statics {
  Statics({
    this.score,
    this.char,
    this.count,
  });

  Statics.fromJson(dynamic json) {
    score = json['score'];
    char = json['char'];
    count = json['count'];
  }
  num? score;
  String? char;
  num? count;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['score'] = score;
    map['char'] = char;
    map['count'] = count;
    return map;
  }
}

/// tipId : 0
/// clip : 0
/// snr : 26.379105
/// volume : 98

class Info {
  Info({
    this.tipId,
    this.clip,
    this.snr,
    this.volume,
  });

  Info.fromJson(dynamic json) {
    tipId = json['tipId'];
    clip = json['clip'];
    snr = json['snr'];
    volume = json['volume'];
  }
  num? tipId;
  num? clip;
  num? snr;
  num? volume;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['tipId'] = tipId;
    map['clip'] = clip;
    map['snr'] = snr;
    map['volume'] = volume;
    return map;
  }
}

/// stressref : 0
/// tonescore : 0
/// dur : 390
/// char : "Catch"
/// liaisonref : 0
/// liaisonscore : 0
/// senseref : 0
/// start : 390
/// fluency : 95
/// score : 91
/// stressscore : 0
/// toneref : 0
/// sensescore : 0
/// phone : [{"phid":"1","ph2alpha":"c","start":390,"char":"k","score":86,"end":540,"pherr":0},{"phid":"2","ph2alpha":"a","start":540,"char":"ae","score":89,"end":600,"pherr":0},{"phid":"3_4_5","ph2alpha":"tch","start":600,"char":"ch","score":97,"end":780,"pherr":0}]
/// end : 780
/// accent : "ea"

class Details {
  Details({
    // this.stressref,
    // this.tonescore,
    // this.dur,
    this.char,
    // this.liaisonref,
    // this.liaisonscore,
    // this.senseref,
    // this.start,
    this.fluency,
    this.score,
    // this.stressscore,
    // this.toneref,
    // this.sensescore,
    this.phone,
    // this.end,
    // this.accent,
    this.snt_details,
    this.text,
    this.chn_char,
  });

  Details.fromJson(dynamic json) {
    // stressref = json['stressref'];
    // tonescore = json['tonescore'];
    // dur = json['dur'];
    char = json['char'];
    // liaisonref = json['liaisonref'];
    // liaisonscore = json['liaisonscore'];
    // senseref = json['senseref'];
    // start = json['start'];
    fluency = null;
    if (json['fluency'] != null) {
      if (json['fluency'] is Map<String, dynamic> ||
          json['fluency'] is Map<Object?, Object?>) {
        fluency = Fluency.fromJson(json['fluency']);
      } else {
        fluency =
            Fluency(overall: (json['fluency'] is num) ? json['fluency'] : 0);
      }
    }
    score = json['score'];
    // stressscore = json['stressscore'];
    // toneref = json['toneref'];
    // sensescore = json['sensescore'];
    if (json['phone'] != null) {
      phone = [];
      json['phone'].forEach((v) {
        phone?.add(Phone.fromJson(v));
      });
    }
    // end = json['end'];
    // accent = json['accent'];
    if (json['snt_details'] != null) {
      snt_details = [];
      json['snt_details'].forEach((v) {
        snt_details?.add(SntDetail.fromJson(v));
      });
    }
    text = json['text'];
    chn_char = json['chn_char'];
  }
  // num? stressref;
  // num? tonescore;
  // num? dur;
  String? char;
  // num? liaisonref;
  // num? liaisonscore;
  // num? senseref;
  // num? start;
  Fluency? fluency;
  num? score;
  // num? stressscore;
  // num? toneref;
  // num? sensescore;
  List<Phone>? phone;
  // num? end;
  // String? accent;
  List<SntDetail>? snt_details;
  String? text;
  String? chn_char;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    // map['stressref'] = stressref;
    // map['tonescore'] = tonescore;
    // map['dur'] = dur;
    map['char'] = char;
    // map['liaisonref'] = liaisonref;
    // map['liaisonscore'] = liaisonscore;
    // map['senseref'] = senseref;
    // map['start'] = start;
    map['fluency'] = fluency;
    map['score'] = score;
    // map['stressscore'] = stressscore;
    // map['toneref'] = toneref;
    // map['sensescore'] = sensescore;
    if (phone != null) {
      map['phone'] = phone?.map((v) => v.toJson()).toList();
    }
    // map['end'] = end;
    // map['accent'] = accent;
    if (snt_details != null) {
      map['snt_details'] = snt_details?.map((v) => v.toJson()).toList();
    }
    map['text'] = text;
    map['chn_char'] = chn_char;
    return map;
  }
}

/// phid : "1"
/// ph2alpha : "c"
/// start : 390
/// char : "k"
/// score : 86
/// end : 540
/// pherr : 0

class Phone {
  Phone({
    // this.phid,
    this.ph2alpha,
    // this.start,
    this.char,
    this.score,
    // this.end,
    // this.pherr,
  });

  Phone.fromJson(dynamic json) {
    // phid = json['phid'];
    ph2alpha = json['ph2alpha'];
    // start = json['start'];
    char = json['char'];
    score = json['score'];
    // end = json['end'];
    // pherr = json['pherr'];
  }
  // String? phid;
  String? ph2alpha;
  // num? start;
  String? char;
  num? score;
  // num? end;
  // num? pherr;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    // map['phid'] = phid;
    map['ph2alpha'] = ph2alpha;
    // map['start'] = start;
    map['char'] = char;
    map['score'] = score;
    // map['end'] = end;
    // map['pherr'] = pherr;
    return map;
  }
}

/// pause : 0
/// overall : 92
/// speed : 1

class Fluency {
  Fluency({
    // this.pause,
    this.overall,
    this.speed,
  });

  Fluency.fromJson(dynamic json) {
    // pause = json['pause'];
    overall = json['overall'];
    speed = json['speed'];
  }
  // num? pause;
  num? overall;
  num? speed;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    // map['pause'] = pause;
    map['overall'] = overall;
    map['speed'] = speed;
    return map;
  }
}

/// stress : 67
/// overall : 92
/// tone : 100
/// sense : 100

class Rhythm {
  Rhythm({
    this.stress,
    this.overall,
    this.tone,
    this.sense,
  });

  Rhythm.fromJson(dynamic json) {
    stress = json['stress'];
    overall = json['overall'];
    tone = json['tone'];
    sense = json['sense'];
  }
  num? stress;
  num? overall;
  num? tone;
  num? sense;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['stress'] = stress;
    map['overall'] = overall;
    map['tone'] = tone;
    map['sense'] = sense;
    return map;
  }
}

/// app : {"signature":"006f066563d451495cfcbed0d4158915ffebbea1","userId":"LkDmdL5ZkQvDDsiqUKfXZG","applicationId":"a286","connect_id":"0e8fe07a8d3311ef8a7ee3a95023ed05","deviceId":"b3258ad2-d363-4a6a-a68d-56e475ca960a","warrantId":"671245e5917a9a342ecd67cc669daf30","timestamp":"1729243589"}
/// audio : {"saveAudio":0,"sampleBytes":2,"audioType":"ogg","sampleRate":16000,"channel":1}
/// request : {"request_id":"0e86b7668d3311efbd46b7a9ec3f6b9e","phdet":1,"precision":1,"attachAudioUrl":0,"outputPhones":1,"feedback":0,"refText":"Catch the ball!","symbol":1,"coreType":"en.sent.score","rank":100}

class Params {
  Params({
    this.app,
    this.audio,
    this.request,
  });

  Params.fromJson(dynamic json) {
    app = json['app'] != null ? App.fromJson(json['app']) : null;
    audio = json['audio'] != null ? Audio.fromJson(json['audio']) : null;
    request =
        json['request'] != null ? Request.fromJson(json['request']) : null;
  }
  App? app;
  Audio? audio;
  Request? request;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (app != null) {
      map['app'] = app?.toJson();
    }
    if (audio != null) {
      map['audio'] = audio?.toJson();
    }
    if (request != null) {
      map['request'] = request?.toJson();
    }
    return map;
  }
}

/// request_id : "0e86b7668d3311efbd46b7a9ec3f6b9e"
/// phdet : 1
/// precision : 1
/// attachAudioUrl : 0
/// outputPhones : 1
/// feedback : 0
/// refText : "Catch the ball!"
/// symbol : 1
/// coreType : "en.sent.score"
/// rank : 100

class Request {
  Request({
    this.requestId,
    this.phdet,
    this.precision,
    this.attachAudioUrl,
    this.outputPhones,
    this.feedback,
    this.refText,
    this.symbol,
    this.coreType,
    this.rank,
  });

  Request.fromJson(dynamic json) {
    requestId = json['request_id'];
    phdet = json['phdet'];
    precision = json['precision'];
    attachAudioUrl = json['attachAudioUrl'];
    outputPhones = json['outputPhones'];
    feedback = json['feedback'];
    refText = json['refText'];
    symbol = json['symbol'];
    coreType = json['coreType'];
    rank = json['rank'];
  }
  String? requestId;
  num? phdet;
  num? precision;
  num? attachAudioUrl;
  num? outputPhones;
  num? feedback;
  String? refText;
  num? symbol;
  String? coreType;
  num? rank;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['request_id'] = requestId;
    map['phdet'] = phdet;
    map['precision'] = precision;
    map['attachAudioUrl'] = attachAudioUrl;
    map['outputPhones'] = outputPhones;
    map['feedback'] = feedback;
    map['refText'] = refText;
    map['symbol'] = symbol;
    map['coreType'] = coreType;
    map['rank'] = rank;
    return map;
  }
}

/// saveAudio : 0
/// sampleBytes : 2
/// audioType : "ogg"
/// sampleRate : 16000
/// channel : 1

class Audio {
  Audio({
    this.saveAudio,
    this.sampleBytes,
    this.audioType,
    this.sampleRate,
    this.channel,
  });

  Audio.fromJson(dynamic json) {
    saveAudio = json['saveAudio'];
    sampleBytes = json['sampleBytes'];
    audioType = json['audioType'];
    sampleRate = json['sampleRate'];
    channel = json['channel'];
  }
  num? saveAudio;
  num? sampleBytes;
  String? audioType;
  num? sampleRate;
  num? channel;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['saveAudio'] = saveAudio;
    map['sampleBytes'] = sampleBytes;
    map['audioType'] = audioType;
    map['sampleRate'] = sampleRate;
    map['channel'] = channel;
    return map;
  }
}

/// signature : "006f066563d451495cfcbed0d4158915ffebbea1"
/// userId : "LkDmdL5ZkQvDDsiqUKfXZG"
/// applicationId : "a286"
/// connect_id : "0e8fe07a8d3311ef8a7ee3a95023ed05"
/// deviceId : "b3258ad2-d363-4a6a-a68d-56e475ca960a"
/// warrantId : "671245e5917a9a342ecd67cc669daf30"
/// timestamp : "1729243589"

class App {
  App({
    this.signature,
    this.userId,
    this.applicationId,
    this.connectId,
    this.deviceId,
    this.warrantId,
    this.timestamp,
  });

  App.fromJson(dynamic json) {
    signature = json['signature'];
    userId = json['userId'];
    applicationId = json['applicationId'];
    connectId = json['connect_id'];
    deviceId = json['deviceId'];
    warrantId = json['warrantId'];
    timestamp = json['timestamp'];
  }
  String? signature;
  String? userId;
  String? applicationId;
  String? connectId;
  String? deviceId;
  String? warrantId;
  String? timestamp;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['signature'] = signature;
    map['userId'] = userId;
    map['applicationId'] = applicationId;
    map['connect_id'] = connectId;
    map['deviceId'] = deviceId;
    map['warrantId'] = warrantId;
    map['timestamp'] = timestamp;
    return map;
  }
}

/// param : {"app":{"signature":"006f066563d451495cfcbed0d4158915ffebbea1","userId":"LkDmdL5ZkQvDDsiqUKfXZG","applicationId":"a286","connect_id":"0e8fe07a8d3311ef8a7ee3a95023ed05","deviceId":"b3258ad2-d363-4a6a-a68d-56e475ca960a","warrantId":"671245e5917a9a342ecd67cc669daf30","timestamp":"1729243589"},"sdk":{"os":"linux","product":"fake","os_version":"0.0","source":6,"protocol":1,"type":1,"arch":"aarch64","version":50332416}}
/// cmd : "connect"

class Connect {
  Connect({
    this.param,
    this.cmd,
  });

  Connect.fromJson(dynamic json) {
    param = json['param'] != null ? Param.fromJson(json['param']) : null;
    cmd = json['cmd'];
  }
  Param? param;
  String? cmd;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (param != null) {
      map['param'] = param?.toJson();
    }
    map['cmd'] = cmd;
    return map;
  }
}

/// app : {"signature":"006f066563d451495cfcbed0d4158915ffebbea1","userId":"LkDmdL5ZkQvDDsiqUKfXZG","applicationId":"a286","connect_id":"0e8fe07a8d3311ef8a7ee3a95023ed05","deviceId":"b3258ad2-d363-4a6a-a68d-56e475ca960a","warrantId":"671245e5917a9a342ecd67cc669daf30","timestamp":"1729243589"}
/// sdk : {"os":"linux","product":"fake","os_version":"0.0","source":6,"protocol":1,"type":1,"arch":"aarch64","version":50332416}

class Param {
  Param({
    this.app,
    this.sdk,
  });

  Param.fromJson(dynamic json) {
    app = json['app'] != null ? App.fromJson(json['app']) : null;
    sdk = json['sdk'] != null ? Sdk.fromJson(json['sdk']) : null;
  }
  App? app;
  Sdk? sdk;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    if (app != null) {
      map['app'] = app?.toJson();
    }
    if (sdk != null) {
      map['sdk'] = sdk?.toJson();
    }
    return map;
  }
}

/// os : "linux"
/// product : "fake"
/// os_version : "0.0"
/// source : 6
/// protocol : 1
/// type : 1
/// arch : "aarch64"
/// version : 50332416

class Sdk {
  Sdk({
    this.os,
    this.product,
    this.osVersion,
    this.source,
    this.protocol,
    this.type,
    this.arch,
    this.version,
  });

  Sdk.fromJson(dynamic json) {
    os = json['os'];
    product = json['product'];
    osVersion = json['os_version'];
    source = json['source'];
    protocol = json['protocol'];
    type = json['type'];
    arch = json['arch'];
    version = json['version'];
  }
  String? os;
  String? product;
  String? osVersion;
  num? source;
  num? protocol;
  num? type;
  String? arch;
  num? version;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['os'] = os;
    map['product'] = product;
    map['os_version'] = osVersion;
    map['source'] = source;
    map['protocol'] = protocol;
    map['type'] = type;
    map['arch'] = arch;
    map['version'] = version;
    return map;
  }
}

class SntDetail {
  String? accent;
  num? dur;
  String? char;
  num? score;
  List<Phone>? phone;
  num? end;
  num? start;
  num? tone;
  num? tonescore;
  String? chn_char;

  SntDetail({
    this.accent,
    this.dur,
    this.char,
    this.score,
    this.phone,
    this.end,
    this.start,
    this.tone,
    this.tonescore,
    this.chn_char,
  });

  SntDetail.fromJson(dynamic json) {
    tone = json["tone"];
    accent = json['accent'];
    dur = json['dur'];
    char = json['char'];
    score = json['score'];
    if (json['phone'] != null) {
      phone = [];
      json['phone'].forEach((v) {
        phone?.add(Phone.fromJson(v));
      });
    }
    end = json['end'];
    start = json['start'];
    tonescore = json['tonescore'];
    chn_char = json['chn_char'];
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['accent'] = accent;
    map['dur'] = dur;
    map['char'] = char;
    map['score'] = score;
    if (phone != null) {
      map['phone'] = phone?.map((v) => v.toJson()).toList();
    }
    map['end'] = end;
    map['start'] = start;
    map['tone'] = tone;
    map['tonescore'] = tonescore;
    map['chn_char'] = chn_char;

    return map;
  }
}
