import 'package:lib_base/config/env/env_config.dart';
import 'package:lib_base/config/net/base_response.dart';
import 'package:lib_base/config/storage_manager.dart';
import 'package:lib_base/resource/shared_preferences_keys.dart';
import 'package:skill_up_english/api/app_api.dart';
import 'package:skill_up_english/model/http/etingshuo_token_model.dart';
import 'package:skill_up_english/model/http/login_info_model.dart';
import 'package:skill_up_english/model/http/pt_list_phone_medata_xunfei_model.dart';
import 'package:lib_base/utils/rsa_util.dart';
import 'package:skill_up_english/model/http/schedule_task_model.dart';
import 'package:skill_up_english/model/http/task_detail.dart';


class BaseApiRepository {

  static Future<BaseResponse<EtingshuoTokenModel>> gettingshuoToken() {
    return BaseAppApi.instance().gettingshuoToken();
  }

  static Future<BaseResponse<List<PtListPhoneMedataXunfeiModel>>>
      ptlistphonemedataxunfei({
    required List<PtListPhoneMedataXunfeiParam> phonemes,
  }) {
    return BaseAppApi.instance().ptlistphonemedataxunfei(phonemes);
  }


  static Future<BaseResponse<LoginInfoModel>> login({
    required String loginName,
    required String loginPwd,
  }) async{
    String encodePwd =
        await RsaUtil.encodeStringWithPubKey(Env.envConfig.publicKey, loginPwd);
    return BaseAppApi.instance().login(loginName, encodePwd).then((v) {
      if (v.isSuccess && v.isDataNotNull && v.dataNotNull.token != null) {
        StorageManager.sharedPreferences.setString(SharedPreferencesKeys.token, v.dataNotNull.token!);
      }
      return v;
    });
  }

  static Future<BaseResponse<ScheduleTaskModel>> queryschedule() {
    return BaseAppApi.instance().queryschedule();
  }

  static Future<BaseResponse<BaseResponse<List<TaskDetail>>>> querytaskdetail(String taskWordDetailsId) {
    return BaseAppApi.instance().querytaskdetail(taskWordDetailsId);
  }
}
