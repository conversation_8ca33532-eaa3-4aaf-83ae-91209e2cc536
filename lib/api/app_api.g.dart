// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_api.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element

class _BaseAppApi implements BaseAppApi {
  _BaseAppApi(
    this._dio, {
    this.baseUrl,
    this.errorLogger,
  });

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<BaseResponse<EtingshuoTokenModel>> gettingshuoToken() async {
    final _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<BaseResponse<EtingshuoTokenModel>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api-hub/api/hub/v50/xunfei/gettoken',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<EtingshuoTokenModel> _value;
    try {
      _value = BaseResponse<EtingshuoTokenModel>.fromJson(
        _result.data!,
        (json) => EtingshuoTokenModel.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<List<PtListPhoneMedataXunfeiModel>>>
      ptlistphonemedataxunfei(
          List<PtListPhoneMedataXunfeiParam> phonemes) async {
    final _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {'phonemes': phonemes};
    final _options =
        _setStreamType<BaseResponse<List<PtListPhoneMedataXunfeiModel>>>(
            Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
                .compose(
                  _dio.options,
                  '/api/textbook/v50/hk/ptlistphonemedataxunfei',
                  queryParameters: queryParameters,
                  data: _data,
                )
                .copyWith(
                    baseUrl: _combineBaseUrls(
                  _dio.options.baseUrl,
                  baseUrl,
                )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<List<PtListPhoneMedataXunfeiModel>> _value;
    try {
      _value = BaseResponse<List<PtListPhoneMedataXunfeiModel>>.fromJson(
        _result.data!,
        (json) => json is List<dynamic>
            ? json
                .map<PtListPhoneMedataXunfeiModel>((i) =>
                    PtListPhoneMedataXunfeiModel.fromJson(
                        i as Map<String, dynamic>))
                .toList()
            : List.empty(),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<LoginInfoModel>> login(
    String loginName,
    String loginPwd,
  ) async {
    final _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': true,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {
      'loginName': loginName,
      'loginPwd': loginPwd,
    };
    final _options = _setStreamType<BaseResponse<LoginInfoModel>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/yyb/v1/training/user/login',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<LoginInfoModel> _value;
    try {
      _value = BaseResponse<LoginInfoModel>.fromJson(
        _result.data!,
        (json) => LoginInfoModel.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<ScheduleTaskModel>> queryschedule() async {
    final _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<BaseResponse<ScheduleTaskModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
        .compose(
          _dio.options,
          '/api/yyb/v1/training/study/queryschedule',
          queryParameters: queryParameters,
          data: _data,
        )
        .copyWith(
            baseUrl: _combineBaseUrls(
          _dio.options.baseUrl,
          baseUrl,
        )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<ScheduleTaskModel> _value;
    try {
      _value = BaseResponse<ScheduleTaskModel>.fromJson(
        _result.data!,
        (json) => ScheduleTaskModel.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<BaseResponse<BaseResponse<List<TaskDetail>>>> querytaskdetail(
      String taskConfigDetailsId) async {
    final _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'taskConfigDetailsId': taskConfigDetailsId
    };
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options =
        _setStreamType<BaseResponse<BaseResponse<List<TaskDetail>>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/yyb/v1/training/study/querytaskdetail',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            )));
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late BaseResponse<BaseResponse<List<TaskDetail>>> _value;
    try {
      _value = BaseResponse<BaseResponse<List<TaskDetail>>>.fromJson(
        _result.data!,
        (json) => BaseResponse<List<TaskDetail>>.fromJson(
          json as Map<String, dynamic>,
          (json) => json is List<dynamic>
              ? json
                  .map<TaskDetail>(
                      (i) => TaskDetail.fromJson(i as Map<String, dynamic>))
                  .toList()
              : List.empty(),
        ),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(
    String dioBaseUrl,
    String? baseUrl,
  ) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
