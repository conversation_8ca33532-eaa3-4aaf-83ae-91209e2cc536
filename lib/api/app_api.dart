import 'package:dio/dio.dart';
import 'package:lib_base/config/net/base_response.dart';
import 'package:lib_base/config/net/dio.dart';
import 'package:lib_base/config/net/interceptors/http_extra_key.dart';
import 'package:skill_up_english/model/http/etingshuo_token_model.dart';
import 'package:skill_up_english/model/http/login_info_model.dart';
import 'package:skill_up_english/model/http/pt_list_phone_medata_xunfei_model.dart';
import 'package:retrofit/retrofit.dart';
import 'package:skill_up_english/model/http/schedule_task_model.dart';
import 'package:skill_up_english/model/http/task_detail.dart';


import 'api.dart';

part 'app_api.g.dart';

@RestApi()
abstract class BaseAppApi {
  static BaseAppApi? _instance;

  factory BaseAppApi.instance() {
    return BaseAppApi();
  }

  factory BaseAppApi() {
    _instance ??= _BaseAppApi(dio);
    return _instance!;
  }

  @POST(BaseApi.gettingshuoToken)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<EtingshuoTokenModel>> gettingshuoToken();

  @POST(BaseApi.ptlistphonemedataxunfei)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<PtListPhoneMedataXunfeiModel>>> ptlistphonemedataxunfei(
      @Field("phonemes") List<PtListPhoneMedataXunfeiParam> phonemes);


  @POST(BaseApi.login)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: true,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<LoginInfoModel>> login(
        @Field("loginName") String loginName,
        @Field("loginPwd") String loginPwd,
      );

  @GET(BaseApi.queryschedule)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<ScheduleTaskModel>> queryschedule();


  @GET(BaseApi.querytaskdetail)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<BaseResponse<List<TaskDetail>>>> querytaskdetail(@Query("taskConfigDetailsId") String taskConfigDetailsId);
}
