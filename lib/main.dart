import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/config/app_config.dart';
import 'package:lib_base/config/net/dio.dart';
import 'package:lib_base/config/storage_manager.dart';
import 'package:lib_base/config/utils/app_platforms.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/resource/plugins/screen_plugin.dart';
import 'package:skill_up_english/config/route_define.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:lib_base/ui/smartdialog_custom_widget.dart';

void main() {
  runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();
    await StorageManager.initSp();
    await initAppDependencies(); // 添加这行
    runApp(const ProviderScope(child: MyApp()));
    ScreenPlugin.resetScreen();
  }, (Object obj, StackTrace stack) {
    if (obj is PlatformException) {
      Logger.error(obj.toString(), stack);
    } else {
      Logger.error(obj, stack);
    }
  });
}

class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> {
  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(1280, 800),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (_, child) {
        if (AppPlatform.isTabletByInches(context)) {
          return _materialAppForPad();
        }
        return _materialAppForMobile();
      },
    );
  }

  Widget _materialAppForMobile() {
    return ScreenUtilInit(
      designSize: const Size(667, 375),
      minTextAdapt: true,
      splitScreenMode: true,
      fontSizeResolver: FontSizeResolvers.radius,
      builder: (_, child) {
        return _materialApp();
      },
    );
  }

  Widget _materialAppForPad() {
    return ScreenUtilInit(
        designSize: const Size(1280, 800),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (_, child) {
          return _materialApp();
        });
  }

  Widget _materialApp() {
    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      routerConfig: routerDefine,
      title: AppConfig.appName,
      builder: FlutterSmartDialog.init(loadingBuilder: (msg) {
        return LoadingWidget(
          msg: msg,
        );
      }, toastBuilder: (msg) {
        return ToastWidget(
          msg: msg,
        );
      }),
      theme: ThemeData(
        // textTheme: TextTheme(titleMedium: TextStyle()),
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blueAccent),
        textButtonTheme: TextButtonThemeData(style: TextButton.styleFrom()),
      ),
      darkTheme: ThemeData(brightness: Brightness.dark, colorSchemeSeed: Colors.grey),
      themeMode: ThemeMode.system,
    );
  }
}
