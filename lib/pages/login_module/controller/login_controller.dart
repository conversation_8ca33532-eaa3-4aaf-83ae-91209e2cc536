import 'package:flutter/material.dart';
import 'package:lib_base/config/storage_manager.dart';
import 'package:lib_base/config/utils/ui_util.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/resource/shared_preferences_keys.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:skill_up_english/api/api_repository.dart';
import 'package:skill_up_english/config/route_define.dart';
import 'package:skill_up_english/config/route_utils.dart';
part 'login_controller.g.dart';

@riverpod
class LoginController extends _$LoginController {
  TextEditingController atController = TextEditingController();
  TextEditingController ptController = TextEditingController();
  ValueNotifier<bool> isCheckBox = ValueNotifier(true);
  List<Map<String, dynamic>>? _accountList;

  build() => null;

  initController() {
    _accountList = StorageManager.sharedPreferences
        .getJsonList(SharedPreferencesKeys.saveAccountList);
    Logger.error(_accountList);
    if (_accountList != null) {
      Map<String, dynamic> temp = _accountList![0];
      atController.text = temp.keys.first;
      ptController.text = temp[atController.text];
    }
  }

  checkBoxClick() {
    isCheckBox.value = !isCheckBox.value;
  }

  loginClick() {
    String accountStr = atController.text;
    String passwordStr = ptController.text;
    if (accountStr.isEmpty) {
      showToast('账号不能为空！');
      return;
    }
    // RegExp regExp = RegExp(r'^1[3-9]\d{9}$');
    // if (regExp.hasMatch(accountStr)) {
    //   showToast('账号格式不正确！');
    //   return;
    // }
    if (passwordStr.isEmpty) {
      showToast('密码不能为空！');
      return;
    }
    BaseApiRepository.login(loginName: accountStr, loginPwd: passwordStr)
        .then((v) {
      if (v.isSuccess && v.isDataNotNull) {
        Logger.info(
            "============= login success, token:${v.dataNotNull.token}");
        saveAccount();
        pushReplacePage(RouteName.home);
      }
    });
  }

  //记住密码
  saveAccount() {
    if (isCheckBox.value) {
      Map<String, dynamic> currentAccount = {
        atController.text: ptController.text
      };
      if (_accountList != null) {
        int containsIndex = -1;
        for (int i = 0; i < _accountList!.length; i++) {
          Map<String, dynamic> accountMap = _accountList![i];
          if (accountMap.containsKey(atController.text)) {
            containsIndex = i;
            break;
          }
        }
        Logger.error(containsIndex);
        if (containsIndex != -1) {
          _accountList!.removeAt(containsIndex);
          _accountList!.insert(containsIndex, currentAccount);
        } else {
          _accountList!.add(currentAccount);
        }
        StorageManager.sharedPreferences
            .setJsonList(SharedPreferencesKeys.saveAccountList, _accountList!);
      } else {
        StorageManager.sharedPreferences.setJsonList(
            SharedPreferencesKeys.saveAccountList, [currentAccount]);
      }
    }
  }
}
