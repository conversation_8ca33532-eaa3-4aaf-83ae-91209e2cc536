import 'dart:ui';

import 'package:flutter/material.dart';
import 'dart:math';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lib_base/ui/base_scaffold.dart';
import 'package:skill_up_english/pages/login_module/controller/login_controller.dart';
import 'package:skill_up_english/src/generated/assets.gen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _LoginState();
}

class _LoginState extends ConsumerState<LoginPage> {
  late LoginController _controller;
  @override
  void initState() {
    super.initState();
    _controller = ref.read(loginControllerProvider.notifier);
    _controller.initController();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 收起键盘
        FocusScope.of(context).unfocus();
      },
      behavior: HitTestBehavior.opaque, // 确保整个区域都可点击
      child: BaseScaffold(
        body: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: Assets.images.backgroundImageHome.provider(),
              fit: BoxFit.fill,
            ),
          ),
          child: SingleChildScrollView(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 50.r, bottom: 30.r),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'E英语宝',
                          style: TextStyle(
                            fontSize: 40.sp,
                            color: Color(0xFFA7F1FF),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          ' / ',
                          style: TextStyle(
                            fontSize: 30.sp,
                            color: Color(0x78FFFFFF),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'AI英语训练系统',
                          style: TextStyle(
                            fontSize: 48.sp,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 50.r),
                    width: 680.r,
                    decoration: BoxDecoration(
                      color: Color(0x38125488),
                      borderRadius: BorderRadius.circular(30.r),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _inputBg(
                          Row(
                            children: [
                              Icon(
                                Icons.account_circle,
                                color: Color(0xFFAEB7BF),
                                size: 20.r,
                              ),
                              Expanded(
                                child: TextField(
                                  controller: _controller.atController,
                                  keyboardType: TextInputType.phone,
                                ),
                              ),
                              Transform.rotate(
                                angle: 0.5 * pi,
                                child: Icon(
                                  Icons.arrow_forward_ios,
                                  color: Color(0xFFAEB7BF),
                                  size: 20.r,
                                ),
                              ),
                            ],
                          ),
                        ),
                        _inputBg(
                          Row(
                            children: [
                              Icon(
                                Icons.lock,
                                color: Color(0xFFAEB7BF),
                                size: 20.r,
                              ),
                              Expanded(
                                child: TextField(
                                  controller: _controller.ptController,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: 16.r),
                          width: 362.r,
                          child: InkWell(
                            onTap: () {
                              _controller.checkBoxClick();
                            },
                            child: Row(
                              children: [
                                ValueListenableBuilder(
                                  valueListenable: _controller.isCheckBox,
                                  builder: (_, isCheckBox, child) {
                                    return Icon(
                                      isCheckBox
                                          ? Icons.check_box_sharp
                                          : Icons
                                              .check_box_outline_blank_rounded,
                                      color: Colors.white,
                                      size: 10.r,
                                    );
                                  },
                                ),
                                Padding(
                                  padding: EdgeInsets.only(left: 10.r),
                                  child: Text(
                                    '记住密码',
                                    style: TextStyle(
                                      fontSize: 10.sp,
                                      color: Colors.white.withOpacity(0.9),
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                        _loginButton(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _inputBg(Widget child) {
    return Container(
      margin: EdgeInsets.only(top: 16.r),
      padding: EdgeInsets.symmetric(horizontal: 15.r),
      width: 362.r,
      height: 48.r,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: Colors.white,
      ),
      child: child,
    );
  }

  Widget _loginButton() {
    return Container(
      margin: EdgeInsets.only(top: 30.r),
      height: 80.r,
      width: 300.r,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: Assets.images.startBg.provider(),
          fit: BoxFit.fitWidth,
        ),
      ),
      padding: EdgeInsets.fromLTRB(12.r, 5.r, 13.r, 12.r),
      child: TextButton(
        onPressed: () {
          _controller.loginClick();
        },
        style: ButtonStyle(
          minimumSize: WidgetStatePropertyAll(Size(1.sw, 1.sh)),
          padding: WidgetStatePropertyAll(EdgeInsets.zero),
          overlayColor: WidgetStatePropertyAll(Colors.transparent),
          backgroundColor: WidgetStateColor.resolveWith((state) {
            if (state.contains(WidgetState.pressed)) {
              return Colors.black.withOpacity(0.05);
            }
            return Colors.transparent;
          }),
        ),
        child: Padding(
          padding: EdgeInsets.only(top: 3.r),
          child: Text(
            '立即登录',
            style: TextStyle(
              fontSize: 22.sp,
              color: Color(0xFF4D2222).withOpacity(0.9),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
