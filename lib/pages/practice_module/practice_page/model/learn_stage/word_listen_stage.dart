

import 'package:flutter/cupertino.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:lib_base/config/utils/ui_util.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/model/learn_stage/base_answer_stage.dart';

class WordListenStage extends LearnStage{

  @override
  bool get isAutoNext => true;

  WordListenStage({required super.stateIndex, required super.wordInfo, required super.startCheck,  super.lastResult});

  @override
  Future<StageExecuteResult> executeStageCheck(StageExecuteResult? lastResult) async {
    await Future.delayed(Duration(seconds: 3));

    //你木有初始化 smartdialog, 没法吐司
    // showToast("哇你好棒哦!!!!");
    await Future.delayed(Duration(seconds: 1));
     return StageExecuteResult(success: true, result: null);
  }

  @override
  Future rebuildStage() async{
  }

  @override
  Widget buildView() {
    startCheck();
     return Container(
        child: Column(
          children: [
            Text("播放单词"),
            Text(wordInfo.content),
          ],
        ),
     );
  }
}