


import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PhonemePronounceModel{
  final num score;
  final String content;
  final String phoneticSymbol;

  PhonemePronounceModel({required this.score, required this.content, required this.phoneticSymbol});
}

class PhonemePronounceStatus extends StatelessWidget {
  final List<PhonemePronounceModel> models;
  const PhonemePronounceStatus({super.key, required this.models});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: models.map((e) => PhonemeView(score: e.score, content: e.content, phoneticSymbol: e.phoneticSymbol)).toList(),
    );
  }
}




class PhonemeView extends StatelessWidget {
  final num score;
  final String content;
  final String phoneticSymbol;
  const PhonemeView({super.key, required this.score, required this.content, required this.phoneticSymbol});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 20.r),
      margin: EdgeInsets.symmetric(horizontal: 4.5.r),
      width: 82.r,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        color: Colors.white.withOpacity(0.96),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(content, style: TextStyle(fontSize: 44.sp, color: Color(0xff006DEC), fontWeight: FontWeight.bold)),
          SizedBox(height: 5.r,),
          Text(phoneticSymbol, style: TextStyle(fontSize: 26.sp, color: Colors.black.withOpacity(0.36), fontWeight: FontWeight.w400)),
        ],
      ),
    );
  }
}
