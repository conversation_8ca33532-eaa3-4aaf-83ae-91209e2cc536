

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SimpleBgButton extends StatelessWidget {
  final String content;
  final Color bg;
  final VoidCallback? onTap;
  EdgeInsets padding;


    SimpleBgButton({super.key, required this.content, required this.bg, this.onTap, required this.padding});


    SimpleBgButton.primary({super.key, required this.content, this.onTap, }) :
        bg = const Color(0xFF34B6C9),
        padding=EdgeInsets.symmetric(vertical: 4.r,horizontal: 24.r)
    ;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: padding,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: bg,
        ),
        child: Text(content,style: TextStyle(fontSize: 16.sp, color: Colors.white, fontWeight: FontWeight.normal)),
      ),
    );
  }
}
