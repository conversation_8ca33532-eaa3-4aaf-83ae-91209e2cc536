

import 'package:flutter/cupertino.dart';
import 'package:lib_base/config/utils/ui_util.dart';
import 'package:skill_up_english/model/speech_evaluating_result_info.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/model/learn_stage/base_answer_stage.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/model/learn_stage/reading_word_result_stage/widgets/phoneme_pronounce_status.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/model/learn_stage/reading_word_result_stage/widgets/simple_bg_button.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/widgets/recognize_reading_view/widgets/star_score_row.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/widgets/world_info_view.dart';
import 'package:skill_up_english/src/generated/assets.gen.dart';


//认读结果页, 展示音素得分多
class ReadingWordResultStage extends LearnStage<dynamic, SpeechEvaluationResult>{

  @override
  bool get isAutoNext => false;

  ReadingWordResultStage({required super.stateIndex, required super.wordInfo, required super.startCheck,  super.lastResult});

  @override
  Future<StageExecuteResult> executeStageCheck(StageExecuteResult? lastResult) async {
    await Future.delayed(Duration(seconds: 3));


    showToast("哇你好棒哦!!!!");
    await Future.delayed(Duration(seconds: 1));
     return StageExecuteResult(success: true, result: null);
  }

  @override
  Future rebuildStage() async{
  }

  @override
  Widget buildView() {
    startCheck();
     return Row(
       children: [
         Expanded(
           child: Column(
             children: [
               WorldInfoView(
                 title: 'classroom',
                 phono: '[ˈklɑːs ruːm]',
                 definition: 'n.教室，课堂',
                   vowel:wordInfo.vowel,
               ),
               Padding(
                 padding:   EdgeInsets.only(top: 60.r),
                 child: StarScoreRow(score: lastResult?.result?.overall??0,),
               ),
               SizedBox(height: 50.r),
               // PhonemeView(score: 100, content: 'k', phoneticSymbol: 'k'),
               PhonemePronounceStatus(models: [
                 PhonemePronounceModel(content: 'k', phoneticSymbol: 'k', score: 100),
                 PhonemePronounceModel(content: 'l', phoneticSymbol: 'l', score: 100),
                 PhonemePronounceModel(content: 'a', phoneticSymbol: 'a', score: 100),
               ],),
               SizedBox(height: 20.r,),
               Row(
                 mainAxisAlignment: MainAxisAlignment.center,
                 children: [
                   SimpleBgButton.primary(
                     content: '音节拼读',
                     onTap: () {
                       //todo
                     },
                   ),
                   SizedBox(width: 20.r,),
                   SimpleBgButton.primary(
                     content: '音素拼读',
                     onTap: () {
                       //todo
                     },
                   ),
                   SizedBox(width: 20.r,),
                   SimpleBgButton.primary(
                     content: '真人示范',
                     onTap: () {
                       //todo
                     },
                   ),
                 ],
               )
             ],
           ),
         ),
         Column(
           children: [
             Stack(
               alignment: Alignment.center,
               children: [
                 Assets.images.speakerBg.image(width: 54.r),
                 Assets.images.speakerWhite.image(width: 28.r),
               ],
             )
           ],
         )
       ],
     );
  }
}