import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lib_base/config/utils/app_platforms.dart';
import 'package:lib_base/ui/base_scaffold.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/ui/bordered_text.dart';
import 'package:skill_up_english/config/route_utils.dart';
import 'package:skill_up_english/pages/home_module/home_page/widgets/custom_progress_view.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/widgets/listen_word_view/listen_word_view.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/widgets/recognize_reading_view/recognize_reading_view.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/widgets/recognize_wordView/recognize_word_view.dart';
import 'package:skill_up_english/src/generated/assets.gen.dart';

enum PracticeSceneType {
  recognizeReading('认读'),
  recognizeWord('认词'),
  listenWord('听词'),
  readWord('读词'),
  speakWord('说词'),
  memorizeWord('记词'),
  dictation('听写'),
  recallWriting('默写'),
  sentenceReading('句子朗读'),
  translationEn2Zh('英译中练习');

  final String title;
  const PracticeSceneType(this.title);



}

class PracticePageParameters {
  final PracticeSceneType sceneType;
  final String taskConfigDetailsId;
  PracticePageParameters({required this.sceneType, required this.taskConfigDetailsId});
}

class PracticePage extends ConsumerStatefulWidget {
  final PracticePageParameters parameters;
  const PracticePage({super.key, required this.parameters});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _PracticeState();
}

class _PracticeState extends ConsumerState<PracticePage> {
  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      body: _mainView(),
    );
  }

  Widget _mainView() {
    final bottomOffset = AppPlatform.isHarmony ? 30.r : 10.r;
    return Stack(
      children: [
        Assets.images.backgroundImage.image(fit: BoxFit.cover, height: 1.sh, width: 1.sw),
        SafeArea(
          bottom: AppPlatform.isHarmony ? false : true,
          child: Column(
            children: [
              SizedBox(height: 30.r),
              _topBarView(),
              SizedBox(height: 30.r),
              Expanded(child: _contentView()),
              SizedBox(height: bottomOffset),
            ],
          ),
        )
      ],
    );
  }

  Widget _topBarView() {
    return Row(
      children: [
        SizedBox(width: 38.r),
        IconButton(
            onPressed: () {
              back();
            },
            style: ButtonStyle(
                padding: WidgetStatePropertyAll(EdgeInsets.zero),
                minimumSize: WidgetStatePropertyAll(Size(40.r, 40.r)),
                overlayColor: WidgetStatePropertyAll(Colors.transparent),
                backgroundColor: WidgetStateColor.resolveWith((state) {
                  if (state.contains(WidgetState.pressed)) {
                    return Colors.white.withOpacity(0.9);
                  }
                  return Colors.white;
                })),
            icon: Padding(
              padding: EdgeInsets.only(right: 4.r),
              child: Assets.images.backIcon.image(height: 15.r),
            )),
        SizedBox(width: 24.r),
        BorderedText(
          text: widget.parameters.sceneType.title,
          borderColor: Color(0xFF417d8b),
          strokeWidth: 8.r,
          textStyle:
              TextStyle(letterSpacing: 5.r, fontSize: 24.sp, color: Color(0xFFFFFDE9), fontWeight: FontWeight.w900),
        ),
        SizedBox(width: 24.r),
        Expanded(
            child: CustomProgressView(
          value: 1,
          maxValue: 3,
          height: 24.r,
          padding: EdgeInsets.symmetric(horizontal: 6.r, vertical: 5.r),
          foregroundColor: Color(0xFFFFDE39),
          backgroundColor: Colors.white,
        )),
        SizedBox(width: 24.r),
        _scoreView(),
        SizedBox(width: 38.r),
      ],
    );
  }

  Widget _scoreView() {
    return Stack(
      alignment: Alignment.center,
      children: [
        Assets.images.topElementBg.image(width: 110.r, height: 40.r, fit: BoxFit.fill),
        Row(
          children: [
            Assets.images.starIcon.image(width: 40.r),
            SizedBox(width: 6.r),
            Text('400000', style: TextStyle(fontSize: 18.sp, color: Colors.white, fontWeight: FontWeight.bold))
          ],
        )
      ],
    );
  }

  Widget _contentView() {
    return ClipRRect(
        child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              width: 1.sw,
              margin: EdgeInsets.symmetric(horizontal: 30.r),
              decoration:
                  BoxDecoration(color: Colors.white.withOpacity(0.66), borderRadius: BorderRadius.circular(28.r)),
              child: _subcontentView(),
            )));
  }

  Widget _subcontentView() {
    final sceneType = widget.parameters.sceneType;
    switch (sceneType) {
      //认读
      case PracticeSceneType.recognizeReading:
        return RecognizeReadingView(taskConfigDetailsId: widget.parameters.taskConfigDetailsId,);
      //认词
      case PracticeSceneType.recognizeWord:
        return RecognizeWordView();
      //听词
      case PracticeSceneType.listenWord:
        return ListenWordView();
      default:
        return Container();
    }
  }
}
