// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'listen_word_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$listenWordControllerHash() =>
    r'f8d2c4099442f98e25aa3d869f05d74a0060f99b';

/// See also [ListenWordController].
@ProviderFor(ListenWordController)
final listenWordControllerProvider =
    AutoDisposeNotifierProvider<ListenWordController, Object?>.internal(
  ListenWordController.new,
  name: r'listenWordControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$listenWordControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ListenWordController = AutoDisposeNotifier<Object?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
