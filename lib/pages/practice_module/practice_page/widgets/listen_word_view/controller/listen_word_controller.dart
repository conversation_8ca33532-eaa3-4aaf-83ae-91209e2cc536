import 'package:flutter/src/foundation/change_notifier.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:skill_up_english/model/speech_evaluating_result_info.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/controller/common/base_learn_practice_stage_controller.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/model/learn_stage/base_answer_stage.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/model/learn_stage/reading_word_result_stage/reading_word_result_stage.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/model/learn_stage/word_listen_stage.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/model/word_info.dart';
part 'listen_word_controller.g.dart';

@riverpod
class ListenWordController extends _$ListenWordController {
  build() {}

  initController() {}

  _loadData() {}
}
