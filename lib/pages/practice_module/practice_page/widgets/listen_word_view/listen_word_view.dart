import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/config/theme/theme_color.dart';
import 'package:lib_base/config/theme/theme_font.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/widgets/score_badge_view.dart';
import 'package:skill_up_english/src/generated/assets.gen.dart';

import '../custom_style_button.dart';
import '../replay_button.dart';
import '../vertical_divider_view.dart';

class ListenWordView extends ConsumerStatefulWidget {
  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _ListenWordState();
}

class _ListenWordState extends ConsumerState<ListenWordView> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        Container(
          child: Row(
            children: [
              _contentView(),
              VerticalDividerView(),
              _rightView(),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(bottom: 130.r, right: 140.r),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              ScoreBadgeView(score: 80),
            ],
          ),
        ),
      ],
    );
  }

  Widget _contentView() {
    return Expanded(
      child: Column(
        children: [
          SizedBox(height: 70.r),
          Text(
            '听音选词',
            style: TextStyle(
                    fontSize: 30.sp,
                    color: AppColors.blendColors(
                        foregroundColor: Colors.black.withOpacity(0.4), backgroundColor: AppColors.themeColor))
                .sourceHanSansRegular,
          ),
          Expanded(child: Center(child: LayoutBuilder(builder: (context, constraints) {
            return Container(width: constraints.maxWidth * 0.6, child: _listView());
          })))
        ],
      ),
    );
  }

  Widget _listView() {
    return ListView.separated(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemBuilder: (context, index) {
          return _listItemView();
        },
        separatorBuilder: (context, index) {
          return Container(height: 30.r);
        },
        itemCount: 4);
  }

  Widget _listItemView() {
    return Stack(
      alignment: Alignment.center,
      children: [
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.colorCED4D6,
              borderRadius: BorderRadius.circular(28.r),
            ),
          ),
        ),
        Container(
          width: 1.sw,
          height: 100.r,
          margin: EdgeInsets.only(bottom: 5.r),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(28.r),
              border: Border.all(width: 2.r, color: AppColors.themeColor)),
        ),
        Text(
          'classroom',
          style: TextStyle(
                  fontSize: 44.sp,
                  color: AppColors.blendColors(
                      foregroundColor: Colors.black.withOpacity(0.65), backgroundColor: AppColors.color2580FF))
              .sourceHanSansBold,
        )
      ],
    );
  }

  Widget _rightView() {
    return Container(
      width: 205.r,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 50.r),
          CustomStyleButton(
              icon: Assets.images.alarmClockIcon.image(height: 56.r),
              titleBackground: Assets.images.rightElementBg.image(width: 155.r, height: 48.r),
              title: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '5',
                    style: TextStyle(
                            fontSize: 28.sp,
                            color: AppColors.blendColors(
                                foregroundColor: Colors.black.withOpacity(0.2), backgroundColor: AppColors.themeColor))
                        .sourceHanSansBold,
                  ),
                  Text(
                    's',
                    style: TextStyle(
                            fontSize: 24.sp,
                            color: AppColors.blendColors(
                                foregroundColor: Colors.black.withOpacity(0.2), backgroundColor: AppColors.themeColor))
                        .sourceHanSansRegular,
                  )
                ],
              )),
          SizedBox(height: 20.r),
          ReplayButton(
            score: 40,
            status: PlayStatus.stopped,
            onTap: (status) {},
          ),
          SizedBox(height: 40.r),
        ],
      ),
    );
  }
}
