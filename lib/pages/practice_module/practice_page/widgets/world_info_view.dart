import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/log/log.dart';

class WorldInfoView extends StatelessWidget {
  final String title;
  final String phono;
  final List<String> vowelUnits;
  final String definition;
  //元音标注
  final String? vowel;
  WorldInfoView({required this.title, this.vowelUnits = const [], this.phono = '', this.definition = '', this.vowel});

  @override
  Widget build(BuildContext context) {
    return _mainView();
  }

  Widget _phonoView() {
    return Container(
      margin: EdgeInsets.only(top: 15.r),
      decoration: BoxDecoration(color: Color(0xFF34B6C9).withOpacity(0.1), borderRadius: BorderRadius.circular(40.r)),
      padding: EdgeInsets.symmetric(horizontal: 15.r, vertical: 8.r),
      child: Text(
        phono,
        style: TextStyle(color: Color(0xFF34B6C9), fontSize: 20.sp, fontWeight: FontWeight.w400),
      ),
    );
  }


  // 解析包含元音标记的字符串 (例如 "c[o]l[our]")
  /// 生成一个TextSpan列表，用于富文本显示
 List<TextSpan> _getTitleAndVowel(String vowelString){
   List<TextSpan> spans=[];
   // 使用正则表达式匹配中括号及其内部内容，或者不包含中括号的普通文本
   final RegExp exp = RegExp(r'(\[[^\]]*\])|([^\[\]]+)');

   // 查找所有匹配项
   Iterable<Match> matches = exp.allMatches(vowelString);
   for (final Match m in matches) {
     // 获取当前匹配到的完整字符串
     String matchText = m.group(0)!;
     Logger.info("Found match: $matchText"); // 打印日志，方便调试

     // 判断当前匹配项是否以'['开头并以']'结尾
     if (matchText.startsWith('[') && matchText.endsWith(']')) {
       // 这是中括号包裹的内容 (元音)
       spans.add(
         TextSpan(
           // 去掉首尾的中括号
           text: matchText.substring(1, matchText.length - 1),
           // 使用高亮样式 (例如，不同的颜色)
           style: TextStyle(
             fontSize: 80.sp,
             color: Colors.red, // 高亮颜色，你可以自定义
             fontWeight: FontWeight.bold,
           ),
         ),
       );
     } else {
       // 这是普通的非中括号内容 (辅音)
       spans.add(
         TextSpan(
           text: matchText,
           // 使用默认样式
           style: TextStyle(
             fontSize: 80.sp,
             color: Color(0xFF23464D),
             fontWeight: FontWeight.bold,
           ),
         ),
       );
     }
   }
   return spans;
 }

  //展示标题, 如果有元音标记, 重点显示元音
  Widget _getTitleView(){
    Widget titleWidget=Text(
      title,
      style: TextStyle(fontSize: 80.sp, color: Color(0xFF23464D), fontWeight: FontWeight.bold),
    );
    //如果元音标注不为空,  则显示元音标注
    if(vowel?.isNotEmpty??false){
      titleWidget=Text.rich(
        TextSpan(
          children: _getTitleAndVowel(vowel!),
        ),
      );
    }
    return titleWidget;
  }

  Widget _mainView() {

    return Container(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (phono.isNotEmpty) Opacity(opacity: 0, child: _phonoView()),
              SizedBox(width: 8.r),
              _getTitleView(),
              SizedBox(width: 8.r),
              if (phono.isNotEmpty) _phonoView()
            ],
          ),
          Text(
            definition,
            style: TextStyle(color: Color(0xFF376B77), fontSize: 30.sp, fontWeight: FontWeight.w400),
          )
        ],
      ),
    );
  }
}
