import 'package:flutter/material.dart';
import 'package:lib_base/config/theme/theme_color.dart';
import 'package:lib_base/config/theme/theme_font.dart';
import 'package:lib_base/ui/bordered_text.dart';
import 'package:skill_up_english/src/generated/assets.gen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

enum ScoreBadgeType {
  practice,
  keepOn,
  excellent;

  Image get background {
    switch (this) {
      case ScoreBadgeType.practice:
        return Assets.images.practiceBackground2.image(height: 142.r, width: 546.r, fit: BoxFit.cover);
      case ScoreBadgeType.keepOn:
        return Assets.images.keepOnBackground2.image(height: 142.r, width: 546.r, fit: BoxFit.cover);
      case ScoreBadgeType.excellent:
        return Assets.images.excellentBackground2.image(height: 142.r, width: 546.r, fit: BoxFit.cover);
    }
  }

  Image get title {
    switch (this) {
      case ScoreBadgeType.practice:
        return Assets.images.practiceTitle.image(height: 68.r);
      case ScoreBadgeType.keepOn:
        return Assets.images.keepOnTitle.image(height: 68.r);
      case ScoreBadgeType.excellent:
        return Assets.images.excellentTitle.image(height: 68.r);
    }
  }

  static ScoreBadgeType getScoreBadgeType(int score) {
    if (score >= 90 && score <= 100) {
      return ScoreBadgeType.excellent;
    } else if (score >= 60 && score < 90) {
      return ScoreBadgeType.keepOn;
    } else {
      return ScoreBadgeType.practice;
    }
  }
}

class ScoreBadgeView extends StatelessWidget {
  final int score;
  ScoreBadgeView({required this.score});
  @override
  Widget build(BuildContext context) {
    return _mainView();
  }

  Widget _mainView() {
    final scoreBadgeType = ScoreBadgeType.getScoreBadgeType(score);
    return Container(
      child: Stack(
        alignment: Alignment.centerLeft,
        children: [
          scoreBadgeType.background,
          Padding(
            padding: EdgeInsets.only(top: 10.r),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(width: 120.r),
                BorderedText(
                    text: '$score',
                    strokeWidth: 7.r,
                    textStyle: TextStyle(
                            fontSize: 64.sp,
                            color: AppColors.blendColors(
                                foregroundColor: Colors.white.withOpacity(0.9), backgroundColor: AppColors.colorFFE55A))
                        .sourceHanSansHeavy),
                SizedBox(
                  width: 2.r,
                ),
                Padding(
                    padding: EdgeInsets.only(top: 24.r),
                    child: BorderedText(
                        text: '分',
                        strokeWidth: 5.r,
                        textStyle: TextStyle(
                                fontSize: 24.sp,
                                color: AppColors.blendColors(
                                    foregroundColor: Colors.white.withOpacity(0.8),
                                    backgroundColor: AppColors.colorFFE55A))
                            .sourceHanSansHeavy)),
                SizedBox(width: 2.r),
                SizedBox(height: 54.r, child: scoreBadgeType.title)
              ],
            ),
          )
        ],
      ),
    );
  }
}
