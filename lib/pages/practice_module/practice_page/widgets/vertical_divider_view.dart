import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class VerticalDividerView extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return _verticalDivider();
  }

  Widget _verticalDivider() {
    return Container(
      width: 1.r,
      decoration: BoxDecoration(
          color: Color(0xFF68C5CF),
          gradient: LinearGradient(begin: Alignment.topLeft, end: Alignment.bottomLeft, colors: [
            Color(0xFF68C5CF).withOpacity(0.0),
            Color(0xFF68C5CF),
            Color(0xFF68C5CF),
            Color(0xFF68C5CF).withOpacity(0.0),
          ])),
    );
  }
}
