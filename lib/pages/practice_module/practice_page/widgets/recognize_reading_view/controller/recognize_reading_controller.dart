import 'package:flutter/src/foundation/change_notifier.dart';
import 'package:lib_base/log/log.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:skill_up_english/api/api_repository.dart';
import 'package:skill_up_english/model/speech_evaluating_result_info.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/controller/common/base_learn_practice_stage_controller.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/model/learn_stage/base_answer_stage.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/model/learn_stage/reading_word_result_stage/reading_word_result_stage.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/model/learn_stage/word_listen_stage.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/model/word_info.dart';
part 'recognize_reading_controller.g.dart';

@riverpod
class RecognizeReadingController extends _$RecognizeReadingController with BaseLearnPracticeStageController {
    ValueNotifier<List<WordInfo>> words=ValueNotifier([]);

  late final String taskConfigDetailsId;

  int build() {
    return 0;
  }

  Future initController({required String taskConfigDetailsId}) async{
    this.taskConfigDetailsId=taskConfigDetailsId;
    await _loadData();
  }

 Future _loadData() async{
   await BaseApiRepository.querytaskdetail(taskConfigDetailsId).then((response){
      if(response.isSuccess && response.isDataNotNull){
        words.value=response.dataNotNull.dataNotNull.map((e) => WordInfo(content: e.wordName!,vowel: e.wordDrillVO?.soundmark?.vowel)).toList();
        Logger.info("========= start to load detail data, words:${words.value}");
      }
    });
  }

  @override
  void failCurrentState() async {}

  @override
  Future readyToNextState() async {}

  @override
  LearnStage? getStage(int index, WordInfo? wordInfo) {
    if (wordInfo != null) {
      if (index == 0) {
        return ReadingWordResultStage(
            stateIndex: index,
            wordInfo: wordInfo,
            startCheck: executeStateCheck,
            lastResult: StageExecuteResult(
                success: true,
                result: SpeechEvaluationResult(
                    overall: 90,
                    accuracy: 80,
                    integrity: 80,
                    fluency: Fluency(overall: 100),
                    pron: 100,
                    details: [
                      Details(char: 'ClassRoom', score: 30, phone: [
                        Phone(char: "k", score: 80),
                        Phone(char: "l", score: 60),
                        Phone(char: "a", score: 30),
                        Phone(char: "s", score: 30),
                        Phone(char: "r", score: 90),
                        Phone(char: "u:", score: 50),
                        Phone(char: "m", score: 30),
                      ]),
                    ])));
      } else if (index == 1) {
        return WordListenStage(
          stateIndex: index,
          wordInfo: wordInfo,
          startCheck: executeStateCheck,
        );
      }
    }
    return null;
  }

  @override
  WordInfo? getWord(int index) {
    if (words.value.length > index) {
      return words.value[index];
    }
    return null;
  }
}
