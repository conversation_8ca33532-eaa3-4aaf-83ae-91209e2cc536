// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recognize_reading_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$recognizeReadingControllerHash() =>
    r'74b8f3245c61a0f4295f4be81d73830018022516';

/// See also [RecognizeReadingController].
@ProviderFor(RecognizeReadingController)
final recognizeReadingControllerProvider =
    AutoDisposeNotifierProvider<RecognizeReadingController, int>.internal(
  RecognizeReadingController.new,
  name: r'recognizeReadingControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$recognizeReadingControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RecognizeReadingController = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
