//认读

import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/practice_page.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/widgets/recognize_reading_view/controller/recognize_reading_controller.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RecognizeReadingView extends ConsumerStatefulWidget {

  final String taskConfigDetailsId;
  RecognizeReadingView({super.key, required this.taskConfigDetailsId});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _RecognizeReadingState();
}

class _RecognizeReadingState extends ConsumerState<RecognizeReadingView> {
  late RecognizeReadingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = ref.read(recognizeReadingControllerProvider.notifier);
    _controller.initController(taskConfigDetailsId: widget.taskConfigDetailsId);
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: _controller.words,
      builder: (_, words, child) {
        return Container(
          child: Row(
            children: [
              _contentView(),
            ],
          ),
        );
      }
    );
  }

  Widget _contentView() {
    return Expanded(
        child: SingleChildScrollView(
      child: Column(
        children: [
          SizedBox(height: 40.r),
          ValueListenableBuilder(
              valueListenable: _controller.stageNotifier,
              builder: (_, index, child) {
                return _controller.currentStage?.buildView() ?? const SizedBox.shrink();
              }),
          SizedBox(height: 40.r),
        ],
      ),
    ));
  }
}
