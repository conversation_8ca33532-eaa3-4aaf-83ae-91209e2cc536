import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomStyleButton extends StatelessWidget {
  final Widget icon;
  final Widget titleBackground;
  final Widget title;
  final VoidCallback? onTap;
  CustomStyleButton({required this.icon, required this.title, required this.titleBackground, this.onTap});
  @override
  Widget build(BuildContext context) {
    return _mainView();
  }

  Widget _mainView() {
    return Stack(
      alignment: Alignment.bottomLeft,
      children: [
        titleBackground,
        Positioned.fill(
            child: Padding(
          padding: EdgeInsets.only(right: 20.r),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                children: [
                  icon,
                  Positioned.fill(
                    child: IconButton(
                        onPressed: () {},
                        style: ButtonStyle(
                            padding: WidgetStatePropertyAll(EdgeInsets.zero),
                            overlayColor: WidgetStatePropertyAll(Colors.transparent),
                            backgroundColor: WidgetStateProperty.resolveWith((state) {
                              if (state.contains(WidgetState.pressed)) {
                                return Colors.white.withOpacity(0.15);
                              }
                              return Colors.transparent;
                            })),
                        icon: Container()),
                  )
                ],
              ),
              Expanded(child: Center(child: title))
            ],
          ),
        ))
      ],
    );
  }
}
