import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:lib_base/config/theme/theme_color.dart';
import 'package:lib_base/config/theme/theme_font.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/widgets/custom_style_button.dart';
import 'package:skill_up_english/src/generated/assets.gen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

enum PlayStatus {
  playing, // 播放中
  paused, // 暂停
  stopped; // 停止

  Widget get icon {
    switch (this) {
      case PlayStatus.playing:
        return Assets.images.stopIcon.image(width: 22.r);
      case PlayStatus.paused:
        return Padding(
          padding: EdgeInsets.only(left: 4.r),
          child: Assets.images.playIcon.image(width: 22.r),
        );
      case PlayStatus.stopped:
        return Padding(
          padding: EdgeInsets.only(left: 4.r),
          child: Assets.images.playIcon.image(width: 22.r),
        );
    }
  }
}

enum PerformanceLevel {
  poor, // 红色 - 不及格
  fair, // 橙色 - 良好
  good, // 绿色 - 优秀
  excellent; // 蓝色 - 卓越

  Widget get iconBackground {
    switch (this) {
      case PerformanceLevel.poor:
        return Assets.images.poorIcon.image(width: 52.r);
      case PerformanceLevel.fair:
        return Assets.images.fairIcon.image(width: 52.r);
      case PerformanceLevel.good:
        return Assets.images.goodIcon.image(width: 52.r);
      case PerformanceLevel.excellent:
        return Assets.images.excellentIcon.image(width: 52.r);
    }
  }

  Widget get background {
    switch (this) {
      case PerformanceLevel.poor:
        return Assets.images.poorBackground.image(width: 148.r, height: 48.r);
      case PerformanceLevel.fair:
        return Assets.images.fairIcon.image(width: 148.r, height: 48.r);
      case PerformanceLevel.good:
        return Assets.images.goodBackground.image(width: 148.r, height: 48.r);
      case PerformanceLevel.excellent:
        return Assets.images.excellentBackground.image(width: 148.r, height: 48.r);
    }
  }

  Color get textColor {
    switch (this) {
      case PerformanceLevel.poor:
        return AppColors.colorEB5737;
      case PerformanceLevel.fair:
        return Color(0xFFEC8600);
      case PerformanceLevel.good:
        return Color(0xFF699E38);
      case PerformanceLevel.excellent:
        return AppColors.themeColor;
    }
  }

  static PerformanceLevel fromScore(int score) {
    if (score >= 90 && score <= 100) {
      return PerformanceLevel.excellent;
    } else if (score >= 75 && score < 90) {
      return PerformanceLevel.good;
    } else if (score >= 60 && score < 75) {
      return PerformanceLevel.fair;
    } else {
      return PerformanceLevel.poor;
    }
  }
}

class ReplayButton extends StatefulWidget {
  final int score;
  PlayStatus status;
  final Function(PlayStatus status)? onTap;
  ReplayButton({required this.score, this.status = PlayStatus.stopped, this.onTap});
  @override
  State<StatefulWidget> createState() => _ReplayButtonState();
}

class _ReplayButtonState extends State<ReplayButton> {
  @override
  Widget build(BuildContext context) {
    return _mainView();
  }

  Widget _mainView() {
    final performanceLevel = PerformanceLevel.fromScore(widget.score);

    return CustomStyleButton(
        onTap: () {
          if (widget.status == PlayStatus.paused || widget.status == PlayStatus.stopped) {
            setState(() {
              widget.status = PlayStatus.playing;
            });
          }
          if (widget.status == PlayStatus.playing) {
            setState(() {
              widget.status = PlayStatus.stopped;
            });
          }
          final callback = widget.onTap;
          if (callback != null) {
            callback(widget.status);
          }
        },
        icon: Stack(
          alignment: Alignment.center,
          children: [
            performanceLevel.iconBackground,
            widget.status.icon,
          ],
        ),
        titleBackground: Container(margin: EdgeInsets.only(top: 4.r), child: performanceLevel.background),
        title: Padding(
          padding: EdgeInsets.only(bottom: 2.r),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${widget.score}',
                style: TextStyle(fontSize: 24.sp, color: performanceLevel.textColor).sourceHanSansBold,
              ),
              Padding(
                padding: EdgeInsets.only(top: 4.r),
                child: Text(
                  '分',
                  style: TextStyle(fontSize: 16.sp, color: performanceLevel.textColor).sourceHanSansBold,
                ),
              )
            ],
          ),
        ));
  }
}
