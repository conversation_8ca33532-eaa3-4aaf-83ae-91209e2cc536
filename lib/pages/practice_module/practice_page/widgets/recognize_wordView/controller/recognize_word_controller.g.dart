// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recognize_word_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$recognizeWordControllerHash() =>
    r'c97a02b08332f9764365476bf4a4c4b6a8f628bc';

/// See also [RecognizeWordController].
@ProviderFor(RecognizeWordController)
final recognizeWordControllerProvider =
    AutoDisposeNotifierProvider<RecognizeWordController, int>.internal(
  RecognizeWordController.new,
  name: r'recognizeWordControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$recognizeWordControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RecognizeWordController = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
