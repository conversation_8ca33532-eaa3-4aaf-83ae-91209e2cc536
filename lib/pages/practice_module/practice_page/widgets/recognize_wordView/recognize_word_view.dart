import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/config/theme/theme_color.dart';
import 'package:lib_base/config/theme/theme_font.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/widgets/custom_style_button.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/widgets/replay_button.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/widgets/world_info_view.dart';
import 'package:skill_up_english/src/generated/assets.gen.dart';

import '../vertical_divider_view.dart';

class RecognizeWordView extends ConsumerStatefulWidget {
  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _RecognizeWordState();
}

class _RecognizeWordState extends ConsumerState<RecognizeWordView> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        children: [
          _contentView(),
          VerticalDividerView(),
          _rightView(),
        ],
      ),
    );
  }

  Widget _contentView() {
    return Expanded(
        child: Container(
      child: Column(
        children: [
          SizedBox(height: 40.r),
          WorldInfoView(
            title: 'classroom',
            phono: '[ˈklɑːs ruːm]',
            definition: 'n.教室，课堂',
          ),
          SizedBox(height: 20.r),
          _gridView(),
          SizedBox(height: 40.r),
        ],
      ),
    ));
  }

  Widget _gridView() {
    return Expanded(
      child: Container(
        width: 0.83.sw,
        alignment: Alignment.center,
        child: LayoutBuilder(builder: (context, constraints) {
          return Container(
            width: constraints.maxWidth * 0.83.r,
            child: LayoutBuilder(builder: (context, constraints) {
              final maxWidth = constraints.maxWidth;
              final maxHeight = constraints.maxHeight;
              final ratio = (maxWidth - 30.r) / (maxHeight - 16.r);
              return GridView.builder(
                  padding: EdgeInsets.zero,
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: 4,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2, crossAxisSpacing: 30.r, mainAxisSpacing: 15.r, childAspectRatio: ratio),
                  itemBuilder: (context, index) {
                    return _gridItemView(index: index);
                  });
            }),
          );
        }),
      ),
    );
  }

  Widget _gridItemView({required int index}) {
    return Container(
      child: Column(
        children: [
          Expanded(
              child: Stack(
            children: [
              Container(
                width: 1.sw,
                height: 1.sh,
                decoration:
                    BoxDecoration(color: Colors.black.withOpacity(0.15), borderRadius: BorderRadius.circular(24.r)),
              ),
              Container(
                width: 1.sw,
                height: 1.sh,
                decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(24.r)),
                margin: EdgeInsets.only(bottom: 5.r),
                alignment: Alignment.center,
                child: Container(
                  margin: EdgeInsets.only(top: 14.r, bottom: 12.r),
                  color: Colors.purple,
                  height: 1.sh,
                  width: 280.r,
                ),
              )
            ],
          )),
          SizedBox(height: 5.r),
          Text(
            "图书馆",
            style: TextStyle(color: Colors.black.withOpacity(0.65), fontSize: 20.sp, fontWeight: FontWeight.normal),
          )
        ],
      ),
    );
  }

  Widget _rightView() {
    return Container(
      width: 205.r,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(height: 50.r),
          CustomStyleButton(
              icon: Assets.images.alarmClockIcon.image(height: 56.r),
              titleBackground: Assets.images.rightElementBg.image(width: 155.r, height: 48.r),
              title: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '5',
                    style: TextStyle(
                            fontSize: 28.sp,
                            color: AppColors.blendColors(
                                foregroundColor: Colors.black.withOpacity(0.2), backgroundColor: AppColors.themeColor))
                        .sourceHanSansBold,
                  ),
                  Text(
                    's',
                    style: TextStyle(
                            fontSize: 24.sp,
                            color: AppColors.blendColors(
                                foregroundColor: Colors.black.withOpacity(0.2), backgroundColor: AppColors.themeColor))
                        .sourceHanSansRegular,
                  )
                ],
              )),
          SizedBox(height: 20.r),
          ReplayButton(
            score: 40,
            status: PlayStatus.stopped,
            onTap: (status) {},
          ),
          SizedBox(height: 40.r),
        ],
      ),
    );
  }
}
