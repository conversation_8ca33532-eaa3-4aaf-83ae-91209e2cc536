// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'practice_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$practiceControllerHash() =>
    r'd9e03477a3aa91fd8eb80b6dfd5281f6411ba022';

/// See also [PracticeController].
@ProviderFor(PracticeController)
final practiceControllerProvider =
    AutoDisposeNotifierProvider<PracticeController, Object?>.internal(
  PracticeController.new,
  name: r'practiceControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$practiceControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PracticeController = AutoDisposeNotifier<Object?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
