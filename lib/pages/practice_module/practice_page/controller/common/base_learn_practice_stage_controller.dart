



import 'package:flutter/cupertino.dart';
import 'package:lib_base/log/log.dart';
import 'package:skill_up_english/config/route_utils.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/model/learn_stage/base_answer_stage.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/model/word_info.dart';




//基础的阶段控制器
mixin BaseLearnPracticeStageController  {



    final ValueNotifier<int> stageNotifier= ValueNotifier(0);


  //当前阶段
  LearnStage? getStage(int index, WordInfo? wordInfo) ;

  LearnStage? get currentStage => getStage(stageNotifier.value, currentWord);


  //当前阶段是否已经完成   --  那些不自动进入下一步的阶段, 完成后 isCurrentComplete 会变成true
  ValueNotifier<bool> isCurrentCompleteNotifier=ValueNotifier(false);

  StageExecuteResult? lastResult;

    Map<int,StageExecuteResult> resultMap={};


    final ValueNotifier<int> wordIndexNotifier=ValueNotifier(0);



  WordInfo? getWord(int index);

  WordInfo? get currentWord => getWord(wordIndexNotifier.value);




  //下一个单词
   toNextWord() {
     Logger.info("=========当前单词:${wordIndexNotifier.value}");
    wordIndexNotifier.value++;
    if(currentWord!=null){
      resultMap.clear();
      stageNotifier.value=0;
    }else{
      //结束了
      practiceComplete();
    }
  }

  void practiceComplete(){
    back();
  }




  Future toNext() async {
    //自动进入下一阶段
    await readyToNextState();
    stageNotifier.value++;
    if(currentStage==null){
       finishWordPractice();
    }
  }

  //执行当前阶段的检查, 确定是否通过当前阶段
  Future executeStateCheck() async {
    isCurrentCompleteNotifier.value=false;
    Logger.info("=============当前阶段:${stageNotifier.value}, currentStage:${currentStage}");
    if(currentStage!=null){
      return currentStage!.executeStageCheck(lastResult).then((result) async {
        resultMap[stageNotifier.value]=result;
        lastResult=result;
        if(result.success){
            //当前阶段通过了
            if(currentStage!.isAutoNext){
             await toNext();
            }else{
              //当前阶段变成完成状态
              isCurrentCompleteNotifier.value=true;
            }
        }else{
          //当前阶段没通过
          failCurrentState();
        }
      });
    }else{
      //是最后一个阶段
      await finishWordPractice();
    }
  }

  //当前阶段没通过,  根据不同的流程处理
  void failCurrentState();


  //准备去下一个阶段, 比如有些阶段有动画的,那么可以在这里播放
  Future readyToNextState();

  //完成当前word
  Future finishWordPractice(){
     return toNextWord();
  }


}