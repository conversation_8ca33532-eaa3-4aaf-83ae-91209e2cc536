import 'package:flutter/cupertino.dart';
import 'package:lib_base/config/storage_manager.dart';
import 'package:lib_base/config/utils/ui_util.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/resource/shared_preferences_keys.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:skill_up_english/api/api_repository.dart';
import 'package:skill_up_english/config/route_define.dart';
import 'package:skill_up_english/config/route_utils.dart';
import 'package:skill_up_english/model/http/schedule_task_model.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/practice_page.dart';
part 'home_controller.g.dart';

@riverpod
class HomeController extends _$HomeController {
  ValueNotifier<ScheduleTaskModel?> taskNotifier = ValueNotifier(null);

  build() {
    return null;
  }

  initController() {
    _loadData();
  }

  _loadData() {
    //查询任务列表
    BaseApiRepository.queryschedule().then((response) {
      if (response.isSuccess && response.isDataNotNull) {
        taskNotifier.value = response.dataNotNull;
      }
    });
  }

  refreshData() {
    _loadData();
  }

  toOutLogin() {
    //移除token并前往登录页
    StorageManager.sharedPreferences.remove(SharedPreferencesKeys.token);
    clearAndNavigate(RouteName.login);
  }

  toPracticePage(
      {required PracticeSceneType sceneType, required TaskConfigList task}) {
    if (task.taskConfigDetailsId?.isNotEmpty ?? false) {
      final parameters = PracticePageParameters(
          sceneType: sceneType, taskConfigDetailsId: task.taskConfigDetailsId!);
      toPage(RouteName.practice, extra: parameters);
    } else {
      showToast("任务id为空");
    }
  }

  //开始练习
  void startPractice({int index = 0}) {
    if (taskNotifier.value != null &&
        taskNotifier.value!.taskConfigList!.isNotEmpty) {
      PracticeSceneType sceneType = getSceneType(index);
      toPracticePage(
          task: taskNotifier.value!.taskConfigList![index],
          sceneType: sceneType);
    }
  }

  PracticeSceneType getSceneType(int index) {
    //todo 这个地方   type的对应关系不太清楚, 需要和后台沟通, 先这样对付一下
    switch (index.toString()) {
      case "0":
        return PracticeSceneType.recognizeReading;
      // recognizeReading('认读'),
      //   recognizeWord('认词'),
      //   listenWord('听词'),
      //   readWord('读词'),
      //   speakWord('说词'),
      //   memorizeWord('记词'),
      //   dictation('听写'),
      //   recallWriting('默写'),
      //   sentenceReading('句子朗读'),
      //   translationEn2Zh('英译中练习');
      case "1":
        return PracticeSceneType.recognizeWord;
      case "2":
        return PracticeSceneType.listenWord;
      case "3":
        return PracticeSceneType.readWord;
      case "4":
        return PracticeSceneType.speakWord;
      case "5":
        return PracticeSceneType.memorizeWord;
      case "6":
        return PracticeSceneType.dictation;
      case "7":
        return PracticeSceneType.recallWriting;
      case "8":
        return PracticeSceneType.sentenceReading;
      case "9":
        return PracticeSceneType.translationEn2Zh;
      default:
        return PracticeSceneType.dictation;
    }
  }
}
