import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lib_base/config/utils/app_platforms.dart';
import 'package:lib_base/ui/base_scaffold.dart';
import 'package:skill_up_english/model/http/schedule_task_model.dart';
import 'package:skill_up_english/pages/home_module/home_page/controller/home_controller.dart';
import 'package:skill_up_english/pages/home_module/home_page/widgets/custom_progress_view.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/practice_page.dart';
import 'package:skill_up_english/src/generated/assets.gen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/ui/bordered_text.dart';

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _HomeState();
}

class _HomeState extends ConsumerState<HomePage> {
  late HomeController _controller;

  @override
  void initState() {
    super.initState();
    _controller = ref.read(homeControllerProvider.notifier);
    _controller.initController();
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(body: _mainView());
  }

  Widget _mainView() {
    final bottomOffset = AppPlatform.isHarmony ? 30.r : 10.r;
    return Stack(
      children: [
        Assets.images.backgroundImageHome
            .image(fit: BoxFit.cover, height: 1.sh, width: 1.sw),
        SafeArea(
          bottom: AppPlatform.isHarmony ? false : true,
          child: Column(
            children: [
              SizedBox(height: 24.r),
              _topBarView(),
              SizedBox(height: 24.r),
              Expanded(
                child: Row(
                  children: [
                    SizedBox(width: 36.r),
                    _leftListView(),
                    SizedBox(width: 30.r),
                    Expanded(child: _contentListView()),
                    SizedBox(width: 36.r),
                  ],
                ),
              ),
              SizedBox(height: bottomOffset),
            ],
          ),
        ),
      ],
    );
  }

  Widget _topBarView() {
    return Container(
      height: 56.r,
      child: Row(
        children: [
          SizedBox(width: 36.r),
          Stack(
            alignment: Alignment.centerLeft,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 35.r),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Positioned.fill(
                        child: Assets.images.buttonBg1.image(fit: BoxFit.fill)),
                    Container(
                      height: 40.r,
                      padding:
                          EdgeInsets.only(left: 30.r, top: 3.r, right: 18.r),
                      child: Text(
                        '张三三三',
                        style: TextStyle(
                            fontSize: 24.sp,
                            color: Colors.white,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
              ),
              // Assets.images.defaultAvatar.svg(),
              Container(
                width: 56.r,
                height: 56.r,
                padding: EdgeInsets.symmetric(horizontal: 2.r, vertical: 2.r),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(56.r),
                    color: Colors.white),
                child: Assets.images.defaultAvatar.image(),
              ),
            ],
          ),
          const Spacer(),
          _buildButton(
            backgroundImage: Assets.images.buttonBg1.image(height: 40.r),
            icon: Assets.images.starIcon.image(width: 28.r),
            title: '4000',
            textColor: Colors.white,
          ),
          SizedBox(width: 6.r),
          _buildButton(
              backgroundImage: Padding(
                padding: EdgeInsets.only(top: 5.r),
                child: Assets.images.buttonBg2.image(height: 44.r),
              ),
              title: '退出',
              onTap: () {
                _controller.toOutLogin();
              }),
          SizedBox(width: 36.r),
        ],
      ),
    );
  }

  Widget _buildButton({
    required Widget backgroundImage,
    Image? icon,
    required String title,
    Color textColor = const Color(0xFF041224),
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Stack(
        children: [
          backgroundImage,
          Positioned.fill(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (icon != null) icon,
                if (icon != null) SizedBox(width: 5.r),
                Text(
                  title,
                  style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: textColor),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _leftListView() {
    return ClipRRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          width: 240.r,
          height: 1.sh,
          decoration: BoxDecoration(
            border: Border.all(width: 0.75, color: const Color(0xFF999999)),
            borderRadius: BorderRadius.circular(28.r),
            color: const Color(0xFF125488).withOpacity(0.22),
          ),
          child: Column(
            children: [
              SizedBox(height: 20.r),
              Row(
                children: [
                  SizedBox(width: 20.r),
                  _titleView(viewColor: const Color(0xFFA5FFFC), title: '目标单词'),
                  const Spacer(),
                  GestureDetector(
                    onTap: _controller.refreshData,
                    behavior: HitTestBehavior.opaque,
                    child: Assets.images.refreshIcon.image(width: 32.r),
                  ),
                  SizedBox(width: 15.r),
                ],
              ),
              SizedBox(height: 10.r),
              Expanded(
                child: ValueListenableBuilder(
                    valueListenable: _controller.taskNotifier,
                    builder: (_, task, child) {
                      if (task?.wordList?.isNotEmpty ?? false) {
                        List<String> words = task!.wordList!;
                        return ListView.builder(
                          padding: EdgeInsets.only(top: 10.r, bottom: 10.r),
                          shrinkWrap: true,
                          itemCount: words.length,
                          itemExtent: 64.r,
                          itemBuilder: (context, index) {
                            return Container(
                              width: 1.sw,
                              height: 1.sh,
                              padding: EdgeInsets.symmetric(horizontal: 8.r),
                              margin: EdgeInsets.symmetric(
                                  horizontal: 40.r, vertical: 6.h),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12.r),
                                color: Colors.black.withOpacity(0.05),
                              ),
                              child: Center(
                                child: Text(
                                  words[index],
                                  style: TextStyle(
                                    fontSize: 18.sp,
                                    color: const Color(0xFFFDFFEE),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            );
                          },
                        );
                      } else {
                        return const Center(child: Text('暂无数据'));
                      }
                    }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _contentListView() {
    return ValueListenableBuilder(
        valueListenable: _controller.taskNotifier,
        builder: (_, task, child) {
          if (task == null || (task.taskConfigList?.isEmpty ?? true)) {
            return const Center(child: Text('暂无数据'));
          }
          List<TaskConfigList> taskConfigList = task.taskConfigList!;
          return Container(
            decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(28.r)),
            child: Column(
              children: [
                SizedBox(height: 22.r),
                Row(
                  children: [
                    SizedBox(width: 40.r),
                    _titleView(
                        viewColor: const Color(0xFFFFD84A), title: '训练任务'),
                    const Spacer(),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(40.r),
                        color: Color(0xFF34B5D0).withOpacity(0.12),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 4.r),
                      child: Row(
                        children: [
                          SizedBox(width: 26.r),
                          Assets.images.estimatedDurationIcon
                              .image(height: 20.r),
                          SizedBox(width: 6.r),
                          Text('预计训练时长：30分钟',
                              style: TextStyle(fontSize: 16.sp)),
                          SizedBox(width: 14.r),
                        ],
                      ),
                    ),
                    SizedBox(width: 25.r),
                  ],
                ),
                SizedBox(height: 15.r),
                Expanded(
                  child: ListView.separated(
                    padding: EdgeInsets.zero,
                    itemCount: taskConfigList.length,
                    itemBuilder: (context, index) {
                      return _listItemView(
                          index: index, task: taskConfigList[index]);
                    },
                    separatorBuilder: (context, index) {
                      return SizedBox(height: 10.r);
                    },
                  ),
                ),
                SizedBox(height: 8.r),
                _startButton(),
                SizedBox(height: 5.r),
              ],
            ),
          );
        });
  }

  Widget _listItemView({required int index, required TaskConfigList task}) {
    final textStyle = TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w500);
    return GestureDetector(
      onTap: () => _controller.startPractice(index: index),
      behavior: HitTestBehavior.opaque,
      child: Container(
        margin: EdgeInsets.symmetric().copyWith(left: 36.r, right: 45.r),
        padding: EdgeInsets.symmetric(vertical: 18.r),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(14.r)),
        child: Row(
          children: [
            SizedBox(width: 20.r),
            _indexView(
                index: index, isFinished: index == 2, isCurrent: index == 3),
            SizedBox(width: 10.r),
            Text("${task.taskName ?? ""}(${task.wordNum ?? 0}个)",
                style: textStyle),
            Spacer(),
            Text(
              '闯关分数≥60',
              style: textStyle.copyWith(
                  fontSize: 12.sp, color: Colors.black.withOpacity(0.65)),
            ),
            const Spacer(),
            Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                    width: 170.r,
                    child: CustomProgressView(
                        value: 1, maxValue: 10, height: 22.r)),
                Text(
                  '9/10',
                  style: TextStyle(fontSize: 14.sp, color: Color(0xFF1999AC)),
                )
              ],
            ),
            const Spacer(),
            Assets.images.starIcon.image(height: 28.r),
            SizedBox(width: 4.r),
            Container(
              height: 24.r,
              padding: EdgeInsets.symmetric(horizontal: 10.r),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(40.r),
                color: Color(0xFF34B5D0).withOpacity(0.12),
              ),
              child: Center(
                child: Text('10', style: textStyle.copyWith(fontSize: 14.sp)),
              ),
            ),
            SizedBox(width: 24.r),
            Assets.images.estimatedDurationIcon.image(height: 20.r),
            SizedBox(width: 6.r),
            Text('3\'50\"', style: textStyle.copyWith(fontSize: 16.sp)),
            SizedBox(width: 22.r),
          ],
        ),
      ),
    );
  }

  Widget _titleView({required Color viewColor, required String title}) {
    return Row(
      children: [
        Container(
          width: 11.r,
          height: 32.r,
          decoration: BoxDecoration(
            color: viewColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r),
              topRight: Radius.circular(4.r),
              bottomLeft: Radius.circular(4.r),
              bottomRight: Radius.circular(20.r),
            ),
          ),
        ),
        SizedBox(width: 8.r),
        BorderedText(
          text: title,
          strokeWidth: 5.r,
          borderColor: Color(0xFF417d8b), //Colors.black.withOpacity( 0.3),
          textStyle: TextStyle(
              letterSpacing: 5.r,
              fontSize: 24.sp,
              fontWeight: FontWeight.w900,
              color: const Color(0xFFFFFDE9)),
        ),
      ],
    );
  }

  Widget _startButton() {
    return Container(
      height: 64.r,
      width: 230.r,
      decoration: BoxDecoration(
          image: DecorationImage(image: Assets.images.startBg.image().image)),
      padding: EdgeInsets.only(bottom: 12.r, top: 5.r, left: 12.r, right: 13.r),
      child: TextButton(
        onPressed: _controller.startPractice,
        style: ButtonStyle(
          minimumSize: WidgetStatePropertyAll(Size(1.sw, 1.sh)),
          padding: WidgetStatePropertyAll(EdgeInsets.zero),
          overlayColor: WidgetStatePropertyAll(Colors.transparent),
          backgroundColor: WidgetStateColor.resolveWith((state) {
            if (state.contains(WidgetState.pressed)) {
              return Colors.black.withOpacity(0.05);
            }
            return Colors.transparent;
          }),
        ),
        child: Padding(
          padding: EdgeInsets.only(top: 3.r),
          child: Text(
            '开始',
            style: TextStyle(
              fontSize: 22.sp,
              color: Color(0xFF4D2222).withOpacity(0.9),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Widget _indexView(
      {required int index, bool isFinished = false, bool isCurrent = false}) {
    FontWeight fontWeight = isCurrent ? FontWeight.bold : FontWeight.w400;

    Color textColor = isCurrent ? Color(0xFFB6792E) : Colors.black;
    Color backgroundColor = isCurrent ? Color(0xFFFFE55A) : Color(0xFFD2ECF0);

    Widget iconWidget = Text(
      '${index + 1}',
      style:
          TextStyle(fontSize: 14.sp, fontWeight: fontWeight, color: textColor),
    );

    if (isFinished) {
      iconWidget = SizedBox(width: 12.r, child: Assets.images.trueIcon.image());
    }

    return Container(
      width: 26.r,
      height: 26.r,
      decoration: BoxDecoration(
          color: backgroundColor, borderRadius: BorderRadius.circular(26.r)),
      child: Center(child: iconWidget),
    );
  }
}
