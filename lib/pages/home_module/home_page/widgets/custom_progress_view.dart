import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomProgressView extends StatelessWidget {
  final int value;
  final int maxValue;
  final double height;
  final Color foregroundColor;
  final Color backgroundColor;
  final EdgeInsets padding;
  CustomProgressView(
      {required this.value,
      required this.maxValue,
      this.height = 22,
      this.padding = const EdgeInsets.all(3),
      this.foregroundColor = const Color(0xFFB7E6ED),
      this.backgroundColor = const Color(0xFFE7E9EA)});

  @override
  Widget build(BuildContext context) {
    return _progressView(value: value, maxValue: maxValue, height: height);
  }

  Widget _progressView({required int value, required int maxValue, double height = 22}) {
    // assert(value <= maxValue, 'value ($value) 不能大于 maxValue ($maxValue)');

    if (value > maxValue) {
      debugPrint('警告: value ($value) 不能大于 maxValue ($maxValue)');
      value = maxValue;
    }

    final isFilled = value == maxValue;
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          height: height,
          padding: isFilled ? EdgeInsets.zero : padding,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(height), color: backgroundColor),
          child: Row(
            children: [
              Expanded(
                flex: value,
                child: Container(
                  height: 1.sh,
                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(height), color: foregroundColor),
                ),
              ),
              Expanded(flex: maxValue - value, child: SizedBox())
            ],
          ),
        ),
      ],
    );
  }
}
