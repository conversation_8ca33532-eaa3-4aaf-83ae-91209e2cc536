import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
import 'package:lib_base/log/log.dart';

class RouteName {
  static const String splash = '/';
  static const String home = '/home';
  static const String practice = 'practice';
}

final Map<String, GoRouterWidgetBuilder> _baseRouteBuilderMap = {};

final List<RouteBase> baseRoutes = [_goRoute(RouteName.home)];

GoRoute _goRoute(String path, {List<GoRoute>? routes, bool? isLandscape}) {
  // Logger.info("============= path:$path");
  return goRoute(path, _baseRouteBuilderMap, routes: routes, isLandscape: isLandscape);
}

GoRoute goRoute(String path, Map<String, GoRouterWidgetBuilder> routeMap, {List<GoRoute>? routes, bool? isLandscape}) {
  if (routeMap[path] == null) {
    Logger.error("未注册路由和页面的映射关系：$path");
  }
  return GoRoute(
    path: path,
    name: path,
    routes: routes ?? [],
    pageBuilder: (c, state) {
      bool? needLandscape = isLandscape;
      if (needLandscape == null) {
        if (state.uri.queryParameters['isLandscape'] != null) {
          needLandscape = state.uri.queryParameters['isLandscape'] == "true";
        }
        if (needLandscape == null) {
          if (state.extra != null && state.extra is Map && (state.extra as Map)['isLandscape'] != null) {
            needLandscape = (state.extra as Map)['isLandscape'] == "true";
          }
        }
      }
      return CupertinoPage(
        child: routeMap[path]!.call(c, state),
        name: path,
        arguments: {"extra": state.extra, "queryParameters": state.uri.queryParameters, "isLandscape": needLandscape},
      );
    },
  );
}
