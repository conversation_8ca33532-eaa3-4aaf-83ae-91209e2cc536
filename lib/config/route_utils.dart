import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
import 'package:lib_base/config/application.dart';
import 'package:lib_base/log/log.dart';
import 'package:skill_up_english/config/router_manager.dart';

class RouterManager {
  static PageRoute? currentRoute;
  static PageRoute? preRoute;
  static Object? routeResult;

  static String? get currentRouteName {
    if (currentRoute != null) {
      return currentRoute!.settings.name;
    }
    return null;
  }

  static String? get preRouteName {
    if (preRoute != null) {
      return preRoute!.settings.name;
    }
    return null;
  }

  //是否是强制横屏  ture 是  false 强制竖屏  空  根据当前屏幕方向
  static bool? _isLandscap;

  static set isLandsCap(bool? isLandscap) {
    _isLandscap = isLandscap;
  }

  static bool? get isLandscap => _isLandscap;

  //上一次的路由事件类型
  static RouteEventType? lastEvent;
}

back<T>([T? result]) {
  if (Navigator.canPop(Application.getContext())) {
    // if (result != null) {
    RouterManager.routeResult = result;
    // }
    try {
      // Navigator.of(Application.getContext()).pop<T>(result);
      Application.getContext().pop(result);
    } catch (e, stacktrace) {
      Logger.error("========= back failed:$e", stacktrace);
      // https://github.com/flutter/flutter/issues/123369
      Navigator.of(Application.getContext()).pop<T>(result);
      //       try{
      //   Navigator.of(Application.getContext()).removeRoute(route!);
      // } on Exception catch(e) {
      // }
    }
  }
}

//
// //替换并清空栈
void clearAndNavigate(
  String routeName, {
  Map<String, dynamic>? queryParameters,
  Map<String, String>? pathParameters,
  Object? extra,
}) {
  goPage(routeName, queryParameters: queryParameters, pathParameters: pathParameters, extra: extra);
}

void goPage(
  String routeName, {
  Map<String, dynamic>? queryParameters,
  Map<String, String>? pathParameters,
  Object? extra,
}) {
  var context = Application.getContext();
  context.goNamed(
    routeName,
    queryParameters: queryParameters ?? {},
    pathParameters: pathParameters ?? {},
    extra: extra,
  );
}

Future pushReplacePage(
  String routeName, {
  Map<String, dynamic>? queryParameters,
  Map<String, String>? pathParameters,
  Object? extra,
}) {
  var context = Application.getContext();
  return GoRouter.of(
    context,
  ).replaceNamed(routeName, queryParameters: queryParameters ?? {}, pathParameters: pathParameters ?? {}, extra: extra);
  // return completer.future;
}

String currentRouteName() {
  var context = Application.getContext();
  final router = GoRouter.of(context);
  return router.routerDelegate.currentConfiguration.matches.last.matchedLocation.split("/").last;
}

/**
 * 返回到
 */
void backUtil<T>(String utilRouteName, {int? backCount, T? result}) {
  var context = Application.getContext();
  if (result != null) {
    RouterManager.routeResult = result;
  }
  int popCount = 0;
  utilRouteName = utilRouteName.replaceAll("/", "");
  while (((currentRouteName() != utilRouteName) || (backCount != null && (popCount < backCount))) && context.canPop()) {
    Logger.info("============= backUtil currentRouteName() : ${currentRouteName()}, utilRouteName:$utilRouteName");
    context.pop(result);
    popCount++;
  }
}

/**
 * 返回到
 */
void backWithCount<T>({required int backCount, T? result}) {
  var context = Application.getContext();
  int popCount = 0;
  if (result != null) {
    RouterManager.routeResult = result;
  }
  while ((popCount < backCount) && context.canPop()) {
    Logger.info("============= backUtil currentRouteName() : ${currentRouteName()}, popCount:$popCount");
    context.pop(result);
    popCount++;
  }
}

//
// //替换并清清除指定路由   有点问题， 不要用了 可以试试 clearAndNavigate
Future toAndUtil(
  String routeName, {
  String? utilRouteName,
  int? backCount,
  Map<String, dynamic>? queryParameters,
  Map<String, String>? pathParameters,
  Object? extra,
}) {
  var context = Application.getContext();

  int popCount = 0;
  while (((utilRouteName != null && (currentRouteName() != utilRouteName)) ||
          (backCount != null && (popCount < backCount))) &&
      context.canPop()) {
    context.pop();
    popCount++;
  }
  return context.pushNamed(
    routeName,
    queryParameters: queryParameters ?? {},
    pathParameters: pathParameters ?? {},
    extra: extra,
  );
}

///去了能返回
Future toPage(
  String routeName, {
  Map<String, dynamic>? queryParameters,
  Map<String, String>? pathParameters,
  Object? extra,
  String? pageFlag,
}) {
  var context = Application.getContext();
  Completer completer = Completer();
  //添加路由返回的监听器
  if (RouterManager.currentRoute != null) {
    routeObserver.addRouteChangeCallback(RouterManager.currentRoute, (route, preRoute, eventType, result) {
      Logger.info("===================================toPage result:addRouteChangeCallback $result");
      if (eventType == RouteEventType.didPop) {
        if (!completer.isCompleted) {
          completer.complete(result);
        }
        return true;
      }
      return null;
    });
  }
  context
      .pushNamed(routeName, queryParameters: queryParameters ?? {}, pathParameters: pathParameters ?? {}, extra: extra)
      .then((v) {
        Logger.info("===================================toPage result:then");
        if (!completer.isCompleted) {
          completer.complete(v);
        }
      });
  return completer.future.then((v) {
    Logger.info("===================================toPage result:$v");
    return v;
  });
}

// void toLogin() {
//   toPage(RouteName.login);
// }

// Future toWebviewPage({required String url, String title = "", bool showAppBar = true, bool hasStatus = false}) {
//   return toPage(
//     RouteName.webView,
//     queryParameters: {
//       "url": url,
//       "title": title,
//       "showAppBar": showAppBar.toString(),
//       "hasStatus": hasStatus.toString(),
//     },
//   );
// }

// Future toInteractWebviewModulePage({
//   required String moduleId,
//   String? pageName,
//   String? id,
//   bool showBack = false,
//   bool hasStatus = false,
//   bool isFinish = false,
//   bool isLandscape = false,
//   bool hasFull = false,
//   bool webLoading = true,
//   bool hasVideo = false,
//   Map<String, dynamic>? othermap,
// }) {
//   var qp = {
//     ...?othermap,
//     "moduleId": moduleId,
//     "showBack": showBack.toString(),
//     "hasStatus": hasStatus.toString(),
//     "isLandscape": isLandscape.toString(),
//     "webLoading": webLoading.toString(),
//     "hasVideo": hasVideo.toString(),
//   };
//   Logger.info("=======================othermap:${othermap}");
//   if (pageName != null) {
//     qp['pageName'] = pageName;
//   }
//   if (id != null) {
//     qp['id'] = id;
//   }
//   if (isFinish) {
//     // back();
//     qp["flutter_route_timestamp"] = DateTime.now().millisecondsSinceEpoch.toString();
//     return pushReplacePage(RouteName.interactWebviewModulePage, queryParameters: qp);
//   } else {
//     return toPage(RouteName.interactWebviewModulePage, queryParameters: qp);
//   }
// }

//  String downloadUrl = params['downloadUrl']!;
//     params.remove("downloadUrl");
//     String? indexName = params['indexName'];
//     params.remove("indexName");
//     String resourceId = params['resourceId']!;
// Future interactWebViewDownloadZipPage({
//   required String downloadUrl,
//   required String resourceId,
//   String? indexName,
//   bool showBack = false,
//   bool hasStatus = false,
//   bool isLandscape = false,
//   Map<String, dynamic>? othermap,
// }) {
//   var qp = {
//     ...?othermap,
//     "downloadUrl": downloadUrl,
//     "resourceId": resourceId,
//     "showBack": showBack.toString(),
//     "hasStatus": hasStatus.toString(),
//     "isLandscape": isLandscape.toString(),
//   };
//   if (indexName != null) {
//     qp['indexName'] = indexName;
//   }
//   return toPage(RouteName.interactWebViewDownloadZipPage, queryParameters: qp);
// }

//hasstaus 如果为true， 那么会在webview加一个padding top
// Future toInteractWebviewUrlPage({
//   required String url,
//   String? pageName,
//   String? id,
//   bool hasStatus = false,
//   bool showBack = false,
//   bool isFinish = false,
//   bool isLandscape = false,
//   Map<String, dynamic>? othermap,
// }) {
//   var qp = {
//     ...?othermap,
//     "url": url,
//     "showBack": showBack.toString(),
//     "hasStatus": hasStatus.toString(),
//     "isLandscape": isLandscape.toString(),
//   };
//   if (pageName != null) {
//     qp['pageName'] = pageName;
//   }
//   if (id != null) {
//     qp['id'] = id;
//   }
//   if (isFinish) {
//     qp["flutter_route_timestamp"] = DateTime.now().millisecondsSinceEpoch.toString();
//     return pushReplacePage(RouteName.interactWebviewUrlPage, queryParameters: qp);
//   } else {
//     return toPage(RouteName.interactWebviewUrlPage, queryParameters: qp);
//   }
// }

// Future toFullScreenSimpleVideoPage({
//   required String videoUrl,
//   required bool autoPlay,
//   String? videoImageUrl,
//   bool isFile = false,
// }) {
//   return toPage(
//     RouteName.fullScreenVideo,
//     queryParameters: {
//       "videoUrl": videoUrl,
//       "autoPlay": autoPlay.toString(),
//       "videoImageUrl": videoImageUrl,
//       "isFile": isFile.toString(),
//     },
//   );
// }

// Future toHomeWorkVideoPlayPage({
//   required String videoUrl,
//   required bool autoPlay,
//   String? videoImageUrl,
//   bool isHomework = false,
//   bool isFile = false,
//   HomeWorkSubmitParam? param,
// }) {
//   return toPage(
//     RouteName.videoPlayPage,
//     queryParameters: {
//       "videoUrl": videoUrl,
//       "autoPlay": autoPlay.toString(),
//       "videoImageUrl": videoImageUrl,
//       "isFile": isFile.toString(),
//       'isHomework': isHomework.toString(),
//     },
//     extra: param,
//   );
// }

// Future toReadingBeautifulProsePage({required Map<String, String?> parameters}) {
//   return toPage(RouteName.readingBeautifulProse, queryParameters: parameters);
// }

// Future toSetupReadPage() {
//   return toPage(RouteName.stepRead);
// }
