import 'package:flutter/cupertino.dart';
import 'package:lib_base/config/application.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/resource/plugins/screen_plugin.dart';
import 'package:skill_up_english/config/route_utils.dart';

typedef RouteChangeCallback =
    bool? Function(Route<dynamic>? route, Route<dynamic>? previousRoute, RouteEventType eventType, dynamic result);

enum RouteEventType { didPush, didReplace, didPop }

class RouteObserverHelper extends RouteObserver<PageRoute<dynamic>> {
  Map<PageRoute, RouteChangeCallback> _routeChangeCallbacks = {};

  //路由名字加标记， 避免重复
  void addRouteChangeCallback(PageRoute? route, RouteChangeCallback callback) {
    if (route != null) {
      _routeChangeCallbacks[route] = callback;
    }
  }

  //路由pop 或者replace以后， 会自动清理掉
  void removeRouteChangeCallback(PageRoute route) {
    _routeChangeCallbacks.remove(route);
  }

  String getRouteName(String path) {
    if (path.contains("?")) {
      return path.split("?")[0];
    }
    return path;
  }

  void _sendScreenView(
    Route<dynamic>? route,
    Route<dynamic>? previousRoute, {
    RouteEventType eventType = RouteEventType.didPush,
  }) {
    if (route is PageRoute && previousRoute is PageRoute) {
      RouterManager.currentRoute = route;
      RouterManager.preRoute = previousRoute;
      PageRoute p = route;
      Map<String, dynamic> arguments = route.settings.arguments as Map<String, dynamic>;
      bool isPop = eventType == RouteEventType.didPop;
      if (p.settings is CupertinoPage) {
        var page = p.settings as CupertinoPage;
        Logger.info(
          "========current page:${page.child.toString()}, path:${page.name}， is pop: $isPop, preRouteName:${RouterManager.preRouteName},  currentRouteName:${RouterManager.currentRouteName}, arguments:$arguments ， pre arguments:${previousRoute.settings.arguments}",
        );
      }
      _screenOrientationSet(arguments: arguments, isPop: isPop);
      //遍历监听器
      _executeRouteChangeEvent(route, previousRoute, eventType);
    }
  }

  void _screenOrientationSet({required Map<String, dynamic> arguments, bool isPop = false}) {
    Orientation? manualOrientation = Application.manualOrientation;
    //应该设置为横屏
    bool? shouldSwitchToLandscape = arguments['isLandscape'];

    RouterManager.isLandsCap = shouldSwitchToLandscape;
    if (isPop) {
      //是返回
      //返回到设置了横竖屏的页面， 设置横竖屏进去
      if (shouldSwitchToLandscape == true) {
        ScreenPlugin.switchToLandscape();
      } else if (shouldSwitchToLandscape == false) {
        ScreenPlugin.switchToPortrait();
      } else {
        //如果有手动设置过横竖屏， 则重置屏幕
        if (manualOrientation != null) {
          ScreenPlugin.resetScreen();
        }
        //没有横竖屏设置， 啥也不干。
      }
    } else {
      if (shouldSwitchToLandscape == true) {
        //设置为横屏，
        ScreenPlugin.switchToLandscape();
      } else if (shouldSwitchToLandscape == false) {
        // 设置为竖屏
        ScreenPlugin.switchToPortrait();
      } else {
        //该页面不要求是横屏，    如果有手动设置过横竖屏， 重置
        if (manualOrientation != null) {
          ScreenPlugin.resetScreen();
        }
      }
    }
  }

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    _sendScreenView(route, previousRoute);
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    _sendScreenView(newRoute, oldRoute, eventType: RouteEventType.didReplace);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    _sendScreenView(previousRoute, route, eventType: RouteEventType.didPop);
  }

  void _executeRouteChangeEvent(PageRoute route, PageRoute previousRoute, RouteEventType eventType) {
    if (eventType == RouteEventType.didPop) {
      //是返回， 如果有设置返回事件， 那么就执行它

      RouteChangeCallback? routeCallback = _routeChangeCallbacks[route];
      if (routeCallback != null) {
        Logger.info("===================_executeRouteChangeEvent: RouteEventType.didPop");
        routeCallback(route, previousRoute, eventType, RouterManager.routeResult);
        removeRouteChangeCallback(route);
      }
    }
  }
}

bool isCurrent(String routeName) {
  return RouterManager.currentRouteName == routeName;
}

final RouteObserverHelper routeObserver = RouteObserverHelper();
