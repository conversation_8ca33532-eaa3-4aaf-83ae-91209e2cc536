import 'package:go_router/go_router.dart';
import 'package:lib_base/config/application.dart';
import 'package:lib_base/config/net/interceptors/token_interceptor.dart';
import 'package:lib_base/config/storage_manager.dart';
import 'package:lib_base/resource/shared_preferences_keys.dart';
import 'package:skill_up_english/config/route_name.dart';
import 'package:skill_up_english/config/router_manager.dart';
import 'package:skill_up_english/pages/home_module/home_page/home_page.dart';
import 'package:skill_up_english/pages/login_module/login_page.dart';
import 'package:skill_up_english/pages/practice_module/practice_page/practice_page.dart';

class RouteName {
  // static String splash = "splash";
  static String login = '/login';
  static String home = '/home';
  static const String practice = 'practice';
}

final Map<String, GoRouterWidgetBuilder> routeBuilderMap = {
  RouteName.login: (_, state) => const LoginPage(),
  RouteName.home: (_, state) => const HomePage(),
  RouteName.practice: (_, state) {
    final parameters = state.extra as PracticePageParameters;
    return PracticePage(parameters: parameters);
  },
};

final List<RouteBase> routeBases = [
  _goRoute(
    RouteName.login,
  ),
  _goRoute(RouteName.home, routes: [_goRoute(RouteName.practice, routes: [])]),
];

GoRoute _goRoute(String path, {List<GoRoute>? routes}) {
  return goRoute(path, routeBuilderMap, routes: routes);
}

final routerDefine = _routerDefine();

GoRouter _routerDefine() {
  List<RouteBase> routes = [...routeBases];
  String? token =
      StorageManager.sharedPreferences.getString(SharedPreferencesKeys.token);
  return GoRouter(
    navigatorKey: Application.navigatorKey,
    observers: [routeObserver],
    debugLogDiagnostics: false,
    initialLocation: token != null ? RouteName.home : RouteName.login,
    routes: routes,
  );
}
