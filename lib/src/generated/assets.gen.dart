/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/alarm_clock_icon.png
  AssetGenImage get alarmClockIcon =>
      const AssetGenImage('assets/images/alarm_clock_icon.png');

  /// File path: assets/images/back_icon.png
  AssetGenImage get backIcon =>
      const AssetGenImage('assets/images/back_icon.png');

  /// File path: assets/images/background_image.png
  AssetGenImage get backgroundImage =>
      const AssetGenImage('assets/images/background_image.png');

  /// File path: assets/images/background_image_home.png
  AssetGenImage get backgroundImageHome =>
      const AssetGenImage('assets/images/background_image_home.png');

  /// File path: assets/images/button_bg1.png
  AssetGenImage get buttonBg1 =>
      const AssetGenImage('assets/images/button_bg1.png');

  /// File path: assets/images/button_bg2.png
  AssetGenImage get buttonBg2 =>
      const AssetGenImage('assets/images/button_bg2.png');

  /// File path: assets/images/close_icon.png
  AssetGenImage get closeIcon =>
      const AssetGenImage('assets/images/close_icon.png');

  /// File path: assets/images/default_avatar.png
  AssetGenImage get defaultAvatar =>
      const AssetGenImage('assets/images/default_avatar.png');

  /// File path: assets/images/estimated_duration_icon.png
  AssetGenImage get estimatedDurationIcon =>
      const AssetGenImage('assets/images/estimated_duration_icon.png');

  /// File path: assets/images/excellent_background.png
  AssetGenImage get excellentBackground =>
      const AssetGenImage('assets/images/excellent_background.png');

  /// File path: assets/images/excellent_background2.png
  AssetGenImage get excellentBackground2 =>
      const AssetGenImage('assets/images/excellent_background2.png');

  /// File path: assets/images/excellent_icon.png
  AssetGenImage get excellentIcon =>
      const AssetGenImage('assets/images/excellent_icon.png');

  /// File path: assets/images/excellent_title.png
  AssetGenImage get excellentTitle =>
      const AssetGenImage('assets/images/excellent_title.png');

  /// File path: assets/images/fair_background.png
  AssetGenImage get fairBackground =>
      const AssetGenImage('assets/images/fair_background.png');

  /// File path: assets/images/fair_icon.png
  AssetGenImage get fairIcon =>
      const AssetGenImage('assets/images/fair_icon.png');

  /// File path: assets/images/good_background.png
  AssetGenImage get goodBackground =>
      const AssetGenImage('assets/images/good_background.png');

  /// File path: assets/images/good_icon.png
  AssetGenImage get goodIcon =>
      const AssetGenImage('assets/images/good_icon.png');

  /// File path: assets/images/keep_on_background2.png
  AssetGenImage get keepOnBackground2 =>
      const AssetGenImage('assets/images/keep_on_background2.png');

  /// File path: assets/images/keep_on_title.png
  AssetGenImage get keepOnTitle =>
      const AssetGenImage('assets/images/keep_on_title.png');

  /// File path: assets/images/no_data_icon.png
  AssetGenImage get noDataIconPng =>
      const AssetGenImage('assets/images/no_data_icon.png');

  /// File path: assets/images/no_data_icon.svg
  SvgGenImage get noDataIconSvg =>
      const SvgGenImage('assets/images/no_data_icon.svg');

  /// File path: assets/images/no_task_icon.png
  AssetGenImage get noTaskIcon =>
      const AssetGenImage('assets/images/no_task_icon.png');

  /// File path: assets/images/play_icon.png
  AssetGenImage get playIcon =>
      const AssetGenImage('assets/images/play_icon.png');

  /// File path: assets/images/poor_background.png
  AssetGenImage get poorBackground =>
      const AssetGenImage('assets/images/poor_background.png');

  /// File path: assets/images/poor_icon.png
  AssetGenImage get poorIcon =>
      const AssetGenImage('assets/images/poor_icon.png');

  /// File path: assets/images/practice_background2.png
  AssetGenImage get practiceBackground2 =>
      const AssetGenImage('assets/images/practice_background2.png');

  /// File path: assets/images/practice_title.png
  AssetGenImage get practiceTitle =>
      const AssetGenImage('assets/images/practice_title.png');

  /// File path: assets/images/refresh_icon.png
  AssetGenImage get refreshIcon =>
      const AssetGenImage('assets/images/refresh_icon.png');

  /// File path: assets/images/right_element_bg.png
  AssetGenImage get rightElementBg =>
      const AssetGenImage('assets/images/right_element_bg.png');

  /// File path: assets/images/right_icon.png
  AssetGenImage get rightIcon =>
      const AssetGenImage('assets/images/right_icon.png');

  /// File path: assets/images/speaker_bg.png
  AssetGenImage get speakerBg =>
      const AssetGenImage('assets/images/speaker_bg.png');

  /// File path: assets/images/speaker_white.png
  AssetGenImage get speakerWhite =>
      const AssetGenImage('assets/images/speaker_white.png');

  /// File path: assets/images/star_empty.png
  AssetGenImage get starEmpty =>
      const AssetGenImage('assets/images/star_empty.png');

  /// File path: assets/images/star_full.png
  AssetGenImage get starFull =>
      const AssetGenImage('assets/images/star_full.png');

  /// File path: assets/images/star_icon.png
  AssetGenImage get starIcon =>
      const AssetGenImage('assets/images/star_icon.png');

  /// File path: assets/images/start_bg.png
  AssetGenImage get startBg =>
      const AssetGenImage('assets/images/start_bg.png');

  /// File path: assets/images/stop_icon.png
  AssetGenImage get stopIcon =>
      const AssetGenImage('assets/images/stop_icon.png');

  /// File path: assets/images/top_element_bg.png
  AssetGenImage get topElementBg =>
      const AssetGenImage('assets/images/top_element_bg.png');

  /// File path: assets/images/true_icon.png
  AssetGenImage get trueIcon =>
      const AssetGenImage('assets/images/true_icon.png');

  /// File path: assets/images/voice_icon.png
  AssetGenImage get voiceIcon =>
      const AssetGenImage('assets/images/voice_icon.png');

  /// Directory path: assets/images
  String get path => 'assets/images';

  /// List of all assets
  List<dynamic> get values => [
    alarmClockIcon,
    backIcon,
    backgroundImage,
    backgroundImageHome,
    buttonBg1,
    buttonBg2,
    closeIcon,
    defaultAvatar,
    estimatedDurationIcon,
    excellentBackground,
    excellentBackground2,
    excellentIcon,
    excellentTitle,
    fairBackground,
    fairIcon,
    goodBackground,
    goodIcon,
    keepOnBackground2,
    keepOnTitle,
    noDataIconPng,
    noDataIconSvg,
    noTaskIcon,
    playIcon,
    poorBackground,
    poorIcon,
    practiceBackground2,
    practiceTitle,
    refreshIcon,
    rightElementBg,
    rightIcon,
    speakerBg,
    speakerWhite,
    starEmpty,
    starFull,
    starIcon,
    startBg,
    stopIcon,
    topElementBg,
    trueIcon,
    voiceIcon,
  ];
}

class $AssetsSpeechEvaluationGen {
  const $AssetsSpeechEvaluationGen();

  /// File path: assets/speech_evaluation/etingshuo_phone_symbol.csv
  String get etingshuoPhoneSymbol =>
      'assets/speech_evaluation/etingshuo_phone_symbol.csv';

  /// File path: assets/speech_evaluation/etingshuo_tbphonemescorerule.csv
  String get etingshuoTbphonemescorerule =>
      'assets/speech_evaluation/etingshuo_tbphonemescorerule.csv';

  /// Directory path: assets/speech_evaluation
  String get path => 'assets/speech_evaluation';

  /// List of all assets
  List<String> get values => [
    etingshuoPhoneSymbol,
    etingshuoTbphonemescorerule,
  ];
}

class Assets {
  const Assets._();

  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsSpeechEvaluationGen speechEvaluation =
      $AssetsSpeechEvaluationGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
