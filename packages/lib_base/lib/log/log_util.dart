import 'dart:developer';

import 'package:flutter/foundation.dart';

class LogUtil {
  static const int _limitLength = 800;

  static void doLog(String msg) {
    _logEmpyLine();
    if (msg.length < _limitLength) {
      _log(msg);
    } else {
      segmentationLog(msg);
    }
    _logEmpyLine();
  }

  static void _log(String msg) {
    if (kReleaseMode) {
      debugPrint(msg);
    } else {
      // log(msg);
      debugPrint(msg);
    }
  }

  static void segmentationLog(String msg) {
    var outStr = StringBuffer();
    for (var index = 0; index < msg.length; index++) {
      outStr.write(msg[index]);
      if (((index % _limitLength == 0)|| index==(msg.length-1)) && index != 0) {
        _log(outStr.toString());
        outStr.clear();
        var lastIndex = index + 1;
        if (msg.length - lastIndex < _limitLength) {
          var remainderStr = msg.substring(lastIndex, msg.length);
          _log(remainderStr);
          break;
        }
      }
    }
  }

  static void _logEmpyLine() {
    _log("");
  }
}
