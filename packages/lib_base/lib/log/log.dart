import 'package:flutter/foundation.dart';
import 'package:stack_trace/stack_trace.dart';

import '../config/app_config.dart';
import '../config/env/dart_define.dart';
import 'log_util.dart';

class Logger {
  static _doLog(message) {
    String flag = "💙${kReleaseMode ? "r" : ""} INFO ";
    _doLogWithFlag(message, flag);
  }

  static _doLogWithFlag(message, String flag) {
    if (AppConfig.isDebugMod) {
      var current = Trace.current(3);
      var frame = current.frames[0];
      // var timeStr =
      //     formatDate(DateTime.now(), [HH, ':', nn, ':', ss, ':', SSS]);
      var timeStr = "";

      LogUtil.doLog("$timeStr $frame");
      LogUtil.doLog("$flag:$message");
    }
  }

  static _doLogWithStacktrace(dynamic message, StackTrace? stacktrace) {
    String flag = "❤️${kReleaseMode ? "r" : ""} ERROR ";
    if (stacktrace != null) {
      var trace = Trace.from(stacktrace);
      String msgStr = "$message \n error stacktrace: \n $trace";
      _doLogWithFlag(msgStr, flag);
    } else {
      _doLogWithFlag(message, flag);
    }
  }

  static info(message) {
    _doLog(message);
  }

  static void error(dynamic message, [StackTrace? stacktrace]) {
    _doLogWithStacktrace(message, stacktrace);
  }
}
