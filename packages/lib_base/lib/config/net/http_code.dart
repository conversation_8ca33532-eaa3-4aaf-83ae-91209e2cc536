///   desc   : 错误码
///   version: 1.0
library;

///错误编码
class HttpCode {
  ///没有网络
  static const noNetwork = -1;

  ///未登录
  static const noLogin = -2;
}

///   desc   : 业务错误码
///   version: 1.0

class BusinessCode {
  ///成功

  ///需要登录
  static const needRelogin = "61";
}

/// 这个地方是用错误信息匹配的，
enum eError {
  //接口返回错误代码
  oldPassError("原密码错误"),
  teacherDoing("用户申请教师处理中"),
  parentDoing("用户申请家长处理中"),
  invalidIsbn("无效的书号！"),
  bookDisable("非常抱歉，您查看的图书已被禁用~"),
  bookNoPublish("非常抱歉，您查看的图书还未发布~"),
  bookAdded("图书已收藏过"),
  notFullParams("不完整的参数"),
  loginname_exsits("登录名已存在"),
  loginname_notexsits("登录名不存在"),
  password_error("密码错误"),
  email_error("邮箱地址错误"),
  email_exsits("邮箱地址已存在"),
  over_studentnums("超过最大接纳学生数"),
  over_currencycycle("超过最大每周期货币数"),
  over_ordercycle("超过最大订阅周期"),
  not_exists("不存在的数据"),
  exists("已存在的数据"),
  not_sign_teacher("未申请图书资质的教师"),
  currency_lack("货币数不足"),
  noEmail("用户还未绑定邮箱地址"),
  noFile("该模块没有文件"),
  noUser("用户不存在"),
  hasParise("已对该评论点赞"),
  machUserEmail("邮箱被多人使用"),
  //have_buy_books("该图书已经购买"),
  verify_sended("短信验证码已发送"),
  send_fail("短信验证码发送失败"),
  verify_code_error("短信验证码错误"),
  verify_code_invaild("请输入正确的短信验证码"),
  phone_has_register("手机号码已注册"),
  DuoDianLogin("多点登录"),
  already_evaluate("您已经评论过了"),
  not_enough_point("积分不足"),
  clazz_delete("班级已删除"),
  member_existed("已加入别的班级"),
  approved("申请已被批准"),
  unapproved("申请已被拒绝"),
  no_applys("没有申请记录"),
  clazz_not_exist("班级不存在"),
  fileNotExist("参赛表不存在"),
  teacher_user("老师用户不能加入班级"),
  no_homework("暂无班级练习"),
  max_clazzs("最多创建十个班级"),
  homework_is_deleted("练习已被删除"),
  field_too_long("超过文字限制"),
  student_canceled_apply("学生已取消申请"),
  entry_exsits("已收藏"),
  visitor_login_error("游客不支持账号密码登陆"),
  login_from_teacher("教师账号请从教师端登录"),
  login_from_student("学生账号请从学生端登录"),
  phone_has_other_user_type("存在另一身份注册信息");

  final String value;

  const eError(this.value);

  static String? getMsg(String code) {
    String msg = "";
    for (eError s in eError.values) {
      if (msg.isNotEmpty) {
        break;
      }
      if (code == s.name) {
        msg = s.value;
        break;
      }
    }
    return msg.isEmpty ? null : msg;
  }
}
