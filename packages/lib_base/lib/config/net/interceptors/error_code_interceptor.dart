import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:lib_base/config/utils/ui_util.dart';
// import 'package:lib_base/config/application.dart';
// import 'package:lib_base/src/extension/build_context_ext.dart';
// import 'package:lib_base/config/route_name.dart';
// import 'package:lib_base/widgets/dialog/no_desc_alert_dialog.dart';
import '../../../log/log.dart';
// import '../../../utils/ui_util.dart';
// import '../../route_utils.dart';
import '../http_code.dart';
import 'http_extra_key.dart';

class ErrorCodeInterceptor extends InterceptorsWrapper {
  @override
  onResponse(Response response, handler) async {
    try {
      if (response.data != null && response.data is String) {
        response.data = jsonDecode(response.data!);
      }
    } catch (e, s) {
      Logger.error("=============onResponse response.data is String  parse to map = e:$e", s);
    }
    if (response.data != null && response.data is Map) {
      if (response.data['success']?.toString() != "true") {
        String? code = response.data['code']?.toString();
        String? msg = response.data['msg']?.toString();
        if ((msg != null &&
                (msg == "DuoDianLogin" || msg == '用户无效，请重新登录' || msg == '用户有误，请重新登录' || msg == '用户有误，请退出重进')) ||
            (code != null && code.startsWith(BusinessCode.needRelogin))) {
          //在其他地点登录
          _showReloginDialog();
        } else {
          if (msg != null && msg.isNotEmpty) {
            String msgCode = response.data['msg']!.toString();
            var msg = eError.getMsg(msgCode);
            if (msg != null) {
              //有需要关注的错误
              showToast(msg);
            } else {
              //报错了， 但是错误码不属于需要关注的列表， 根据配置决定是否toast
              var options = response.requestOptions;
              if (options.extra[HttpExtraKey.needErrorToast] == true) {
                if (response.data['msg'] != null && response.data['msg']!.toString().isNotEmpty) {
                  showToast(response.data['msg']!.toString());
                }
              }
            }
          }
        }
      }
    }
    return super.onResponse(response, handler);
  }

  void _showReloginDialog() {
    // showSmartDialog(
    //     NoDescAlertDialog(
    //       singleButton: true,
    //       title: MApplication.getContext().string.accountRelogin,
    //       cancel: () {
    //         dismissDialog();
    //         clearAndNavigate(RouteName.login);
    //       },
    //       confirm: () {
    //         dismissDialog();
    //         clearAndNavigate(RouteName.login);
    //       },
    //     ),
    //     backDismiss: false,
    //     clickMaskDismiss: false);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    var options = err.requestOptions;
    if (options.extra[HttpExtraKey.needErrorToast] == true) {
      if (err.error is SocketException) {
        showToast('连接失败，请检查网络');
      } else {
        showToast("请求失败， 请重试:${err.response?.statusCode}");
      }
    }

    super.onError(err, handler);
  }
}
