import 'package:dio/dio.dart';
import 'package:lib_base/config/storage_manager.dart';
import 'package:lib_base/config/utils/http_util.dart';
import 'package:lib_base/resource/shared_preferences_keys.dart';

import '../../../log/log.dart';
import '../../app_config.dart';
import 'http_extra_key.dart';

class TokenInterceptor extends InterceptorsWrapper {
  String? _token;

  @override
  onRequest(RequestOptions options, handler) {
    //授权码
    getAuthorization();
    addCommonParams(options);
    if (_token != null) {
      options.headers['authorization'] = _token;
    } else {
      if (options.extra[HttpExtraKey.withoutLogin] != true) {
        //需要登录
        // toLogin();
        Logger.error(" 接口${options.baseUrl}${options.path}需要登录");
        return;
      }
    }
    return super.onRequest(options, handler);
  }

  void addCommonParams(
    RequestOptions options,
  ) {
    Map<String, dynamic>? params;
    if (options.method == 'GET') {
      params = options.queryParameters;
    } else {
      if (options.data is Map<String, dynamic>) {
        params = options.data;
      }
    }
    if (params != null) {
      var timestamp = params['timestamp'];
      if (timestamp == null) {
        timestamp = DateTime.now().millisecondsSinceEpoch.toString();
        params['timestamp'] = timestamp;
      }

      // String sign = HttpUtil.getSign(params);
      // if (params['sign'] == null) {
      //   params['sign'] = sign;
      // }
      //其他参数要加在sign后面， 后端校验sign的时候， 只校验了timstamp和那个接口需要的参数， 如果放在sign前面参与了签名的话， 就会导致签名校验不通过
      if (params['phonePlatform'] == null) {
        params['phonePlatform'] = HttpUtil.phonePlatform();
      }
      if (params['appId'] == null) {
        params['appId'] = AppConfig.YYB_APPID.toString();
      }
      if (params['businessId'] == null) {
        params['businessId'] = AppConfig.APP_BUSINESS_ID;
      }
      if (params['versionNum'] == null) {
        params['versionNum'] = AppConfig.buildNumber;
      }
      if (params['deviceId'] == null) {
        params['deviceId'] = AppConfig.deviceId;
      }
    }
  }

  ///获取授权token
  getAuthorization() {
    String? token =
        StorageManager.sharedPreferences.getString(SharedPreferencesKeys.token);
    _token = token;
    return token;
  }
}
