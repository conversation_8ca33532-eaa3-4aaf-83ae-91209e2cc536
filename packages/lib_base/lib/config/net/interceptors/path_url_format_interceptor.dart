import 'package:dio/dio.dart';
import 'package:lib_base/config/env/env_config.dart';
import 'package:lib_base/config/network_config.dart';
import 'package:lib_base/log/log.dart';

import 'http_extra_key.dart';

/// url是否正确检测
class PathUrlFormatInterceptor extends InterceptorsWrapper {
  @override
  onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    var hostPort = options.extra[HttpExtraKey.hostPort];

    HostType hostType = HostType.baseHost;
    //判断host是否需要替换
    var hostName = options.extra[HttpExtraKey.hostName];
    if (hostName != null && hostName is String) {
      hostType = HostType.getTypeByName(hostName);
    }

    String host = Env.getHost(hostType: HostType.baseHost, env: Env.envConfig.env);
    //替换端口
    String newHost = Env.getHost(hostType: hostType, env: Env.envConfig.env, newPort: hostPort);
    options.baseUrl = options.baseUrl.replaceFirst(host, newHost);

    if (options.path.startsWith("http://") || options.path.startsWith("https://")) {
      //path 就是一个完整的链接， 不用做操作
    } else {
      //去掉host 和path之间的重复 /
      if (options.baseUrl.endsWith("/")) {
        options.baseUrl = options.baseUrl.substring(0, options.baseUrl.length - 1);
      }
      if (!options.path.startsWith("/")) {
        options.path = "/${options.path}";
      }
    }

    super.onRequest(options, handler);
  }

  // String getHost(String hostName) {
  //   if (hostName == HostName.payHost) {
  //     return BaseApi.payHost;
  //   } else if (hostName == HostName.ossHost) {
  //     return BaseApi.ossHost;
  //   } else if (hostName == HostName.webHost) {
  //     return BaseApi.webHost;
  //   } else if (hostName == HostName.assetHost) {
  //     return BaseApi.assetHost;
  //   }
  //   return BaseApi.baseHost;
  // }
}
