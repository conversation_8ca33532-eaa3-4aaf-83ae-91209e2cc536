import 'package:dio/dio.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:lib_base/config/utils/ui_util.dart';
import 'http_extra_key.dart';

class LoadingInterceptor extends InterceptorsWrapper {
  @override
  onRequest(RequestOptions options, handler) async {
    if (options.extra[HttpExtraKey.autoLoading] == true) {
      showLoading();
    }
    super.onRequest(options, handler);
  }

  @override
  onResponse(Response response, handler) async {
    if (response.requestOptions.extra[HttpExtraKey.autoLoading] == true) {
      dismissLoading();
    }
    return super.onResponse(response, handler);
  }

  @override
  onError(DioException err, handler) async {
    if (err.requestOptions.extra[HttpExtraKey.autoLoading] == true) {
      dismissLoading();
    }
    return super.onError(err, handler);
  }
}
