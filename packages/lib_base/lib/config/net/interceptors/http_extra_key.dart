class HttpExtraKey {
  ///是否需要自动吐司error
  static const String needErrorToast = "needErrorToast";

  //请求时自动loading
  static const String autoLoading = "auto-loading";

  ///是否不打印request参数
  static const String ignoreRequestParam = "ignoreRequestParam";
  //是否不打印response参数
  static const String ignoreResponseLog = "ignoreResponseLog";

  ///是否需要登录,默认为需要登录
  static const String withoutLogin = "without-login";

  /// host 名称
  static const String hostName = "host_name";

  //  host 端口
  static const String hostPort = "host_port";
}

class HostName {
  static const String payHost = "payHost";
  static const String ossHost = "ossHost";
  static const String baseHost = "baseHost";
  static const String webHost = "webHost";
  static const String assetHost = "assetHost";
  static const String domainhost = "domainhost";
  static const String loghost = "loghost";
}
