import 'dart:io';

import 'package:dio/dio.dart';
import 'package:lib_base/config/utils/app_platforms.dart';
import '../../app_config.dart';

Map<String, dynamic> optHeader = {'content-type': 'application/json;charset=UTF-8'};

class HeaderInterceptor extends InterceptorsWrapper {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    options.headers = optHeader;
    options.headers['platform'] = AppPlatform.platform.name;
    super.onRequest(options, handler);
  }
}
