import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:lib_base/config/env/dart_define.dart';
import 'http_extra_key.dart';
import '../../../log/log.dart';

class CustomLogInterceptor extends InterceptorsWrapper {


  bool urlMatches(List<String> list, String path) {
    for (String element in list) {
      if (element.startsWith("/")) {
        element = element.substring(1);
      }
      if (element.endsWith("/")) {
        element = element.substring(0, element.length - 1);
      }
      if (element == path) {
        return true;
      }
    }
    return false;
  }

  @override
  onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    String path = options.path;
    String baseUrl = options.baseUrl;
    String url = (path.startsWith("http://") || path.startsWith("https://"))
        ? path
        : "$baseUrl$path";
    if (options.extra[HttpExtraKey.ignoreRequestParam] == true) {
      Logger.info(
          '---api-request--->url--> $url, no params log because of config, method:${options.method}');
    } else {
      try{
        if (options.method == 'GET') {
        String qureyParams = "?";
        options.queryParameters.forEach((key, value) {
          qureyParams = "$qureyParams$key=$value&";
        });
        if(qureyParams.endsWith("&")){
          qureyParams = qureyParams.substring(0, qureyParams.length - 1);
        }
        Logger.info(
            '---api-request--->url--> $url$qureyParams,  data:${jsonEncode(options.data)}, header: ${options.headers}, method:${options.method}');
      } else {
        Logger.info("-----options.data type:${options.data.runtimeType}  options.data:${options.data}");
        Logger.info(
            '---api-request--->url--> $url, queryParameters: ${jsonEncode(options.queryParameters)},data:${jsonEncode(options.data)}, header: ${options.headers}, method:${options.method}');
      }
      }catch(e,s){
        Logger.error("----log ---api-request--->url--> $url, request error:$e",s);
      }
    }
    super.onRequest(options, handler);
  }

  @override
  onResponse(
    Response response,
    ResponseInterceptorHandler handler,
  ) {
    var options = response.requestOptions;
    String path = options.path;
    String baseUrl = options.baseUrl;
    String url = (path.startsWith("http://") || path.startsWith("https://"))
        ? path
        : "$baseUrl$path";
    if (options.extra[HttpExtraKey.ignoreResponseLog] == true) {
      Logger.info(
          '---api-response---> url-->$url, no response log because of config, method:${options.method}');
    } else {
      if (DartDefine.simplifyLog == 'false')
        Logger.info(
            '---api-response---> url-->$url , method:${options.method} ,resp----->${response.data != null ? jsonEncode(response.data) : ""}');
    }

    // handler.next(response);
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    var options = err.requestOptions;
    String path = options.path;
    String baseUrl = options.baseUrl;
    String url = (path.startsWith("http://") || path.startsWith("https://"))
        ? path
        : "$baseUrl$path";
    Logger.error(
        "----api-erro---> url --- > $url, method:${options.method}, 请求失败: ${err.message}, error:${err.error}",
        err.stackTrace);
    super.onError(err, handler);
  }
}
