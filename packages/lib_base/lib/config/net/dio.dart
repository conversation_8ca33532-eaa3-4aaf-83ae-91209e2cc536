import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';

import '../app_config.dart';
import 'interceptors/error_code_interceptor.dart';
import 'interceptors/header_interceptor.dart';
import 'interceptors/loading_interceptor.dart';
import 'interceptors/log_interceptor.dart';
import 'interceptors/path_url_format_interceptor.dart';
import 'interceptors/token_interceptor.dart';

// Dio initDioAdapter() {
//   Dio mdio = Dio();
//   mdio.httpClientAdapter = IOHttpClientAdapter();
//   (rootBundle.load("assets/other/eyyb.pem")).then((v) {
//     List<int> clientCertData = v.buffer.asUint8List();
//     (mdio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
//       SecurityContext sc = new SecurityContext();
//       sc.useCertificateChainBytes(clientCertData,
//           password: "1568756__eyyb.vip"); //设置客户端证书到证书链
//       HttpClient client = HttpClient(context: sc);
//       client.badCertificateCallback = (cert, host, port) {
//         return true;
//       };
//       return client;
//     };
//   });
//   return mdio;
// }

// final Dio dio = initDioAdapter()

//   ///设置baseUrl
//   ..options.baseUrl = AppConfig.envConfig.domain

//   ///默认contentType
//   ..options.contentType = ContentType.json.toString()

//   ///超时设置
//   ..options.sendTimeout = const Duration(seconds: 45)
//   ..options.receiveTimeout = const Duration(seconds: 45)
//   ..options.connectTimeout = const Duration(seconds: 45)

//   ///错误后不解析response,避免出现格式解析错误
//   ..options.receiveDataWhenStatusError = false
//   ..interceptors.add(PathUrlFormatInterceptor())
//   ..interceptors.add(HeaderInterceptor())
//   ..interceptors.add(TokenInterceptor())
//   ..interceptors.add(LoadingInterceptor())
//   ..interceptors.add(ErrorCodeInterceptor())
//   ..interceptors.add(CustomLogInterceptor());

Future<Dio> initDioAdapter() async {
  Dio mdio = Dio();
  mdio.httpClientAdapter = IOHttpClientAdapter();

  // 改为await加载证书
  // ByteData v = await rootBundle.load("assets/other/eyyb.pem");
  // List<int> clientCertData = v.buffer.asUint8List();

  // (mdio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
  //   SecurityContext sc = new SecurityContext();
  //   sc.useCertificateChainBytes(clientCertData,
  //       password: "1568756__eyyb.vip"); //设置客户端证书到证书链
  //   HttpClient client = HttpClient(context: sc);
  //   client.badCertificateCallback = (cert, host, port) {
  //     return true;
  //   };
  //   return client;
  // };

  return mdio;
}

Dio dio = Dio(); // 先创建空实例

// 异步初始化方法（放在 main.dart）
Future<void> initAppDependencies() async {
  // 异步初始化真正的 Dio
  final realDio = await initDioAdapter()
    ..options.baseUrl = AppConfig.envConfig.domain
    ..options.contentType = ContentType.json.toString()
    ..options.sendTimeout = const Duration(seconds: 45)
    ..options.receiveTimeout = const Duration(seconds: 45)
    ..options.connectTimeout = const Duration(seconds: 45)
    ..options.receiveDataWhenStatusError = false
    ..interceptors.addAll([
      PathUrlFormatInterceptor(),
      HeaderInterceptor(),
      TokenInterceptor(),
      LoadingInterceptor(),
      ErrorCodeInterceptor(),
      CustomLogInterceptor(),
    ]);

  // 替换全局 dio 实例的内部状态
  dio.options = realDio.options;
  dio.interceptors.clear();
  dio.interceptors.addAll(realDio.interceptors);
  dio.httpClientAdapter = realDio.httpClientAdapter;
}
