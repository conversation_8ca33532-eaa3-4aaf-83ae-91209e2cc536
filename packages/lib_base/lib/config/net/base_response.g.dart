// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BaseResponse<T> _$BaseResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    BaseResponse<T>(
      success: json['success'] as bool?,
      msg: json['msg'] as String?,
      obj: _$nullableGenericFromJson(json['obj'], fromJsonT),
      code: json['code'] as String?,
      attributes: json['attributes'] as String?,
      point: json['point'] as String?,
      messageId: json['messageId'] as String?,
      status: json['status'] as bool?,
    );

Map<String, dynamic> _$BaseResponseToJson<T>(
  BaseResponse<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'success': instance.success,
      'msg': instance.msg,
      'obj': _$nullableGenericToJson(instance.obj, toJsonT),
      'code': instance.code,
      'attributes': instance.attributes,
      'point': instance.point,
      'messageId': instance.messageId,
      'status': instance.status,
    };

T? _$nullableGenericFromJson<T>(
  Object? input,
  T Function(Object? json) fromJson,
) =>
    input == null ? null : fromJson(input);

Object? _$nullableGenericToJson<T>(
  T? input,
  Object? Function(T value) toJson,
) =>
    input == null ? null : toJson(input);
