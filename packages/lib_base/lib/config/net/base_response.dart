import 'package:json_annotation/json_annotation.dart';
import 'package:lib_base/log/log.dart';

import '../env/env_config.dart';
import '../utils/aes_util.dart';

part 'base_response.g.dart';

///   desc   : 网络响应基类
///   version: 1.0
@JsonSerializable(genericArgumentFactories: true)
class BaseResponse<T> {
  bool? success;
  String? msg;
  T? obj;
  String? code;
  String? attributes;
  /**积分*/
  String? point;
  String? messageId;
  bool? status;

  BaseResponse({this.success, this.msg, this.obj, this.code, this.attributes, this.point, this.messageId, this.status});

  factory BaseResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) {
    return _$BaseResponseFromJson(json, fromJsonT);
  }

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) => _$BaseResponseToJson(this, toJsonT);

  ///是否成功
  bool get isSuccess => success ?? false;

  ///是否失败
  bool get isFailure => !isSuccess;

  ///数据是否为空
  bool get isDataNull => obj == null;

  ///数据是否为非空
  bool get isDataNotNull => obj != null;

  // ///成功并且数据不为空

  ///获取非空的数据
  T get dataNotNull {
    // if (obj != null && (status ?? false) && obj is String) {
    //   //对结果进行aes 解密
    //   String CIPHERMODEPADDING = "AES/CBC/PKCS5Padding"; // AES/CBC/PKCS7Padding
    //   String objstr = AesUtil.decryptAes(
    //       encryptedStr: obj!.toString(),
    //       keyStr: Env.envConfig.aesDecodeKey,
    //       ivStr: Env.envConfig.aesIvParameter,
    //       padding: "PKCS7");
    //   return objstr as T;
    // }
    return obj!;
  }
  // BaseResponse<T> onSuccess(void Function(T value) action) {
  //   if (isSuccess && isDataNotNull) action(dataNotNull);
  //   return this;
  // }

  // ///成功并且数据可空
  // BaseResponse<T> onSuccessNullable(void Function(T? value) action) {
  //   if (isSuccess) action(obj);
  //   return this;
  // }

  ///失败
  BaseResponse<T> onFailure(void Function(String? code, String? msg) action) {
    if (isFailure) action(code, msg);
    return this;
  }

  // R requireData<R>(R Function(T? value) onSuccess,
  //     [R Function(String? code, String? msg)? onFailure]) {
  //   if (isSuccess) return onSuccess(obj);
  //   if (onFailure != null) {
  //     return onFailure(code, msg);
  //   }
  //   throw Exception(msg);
  // }
}
