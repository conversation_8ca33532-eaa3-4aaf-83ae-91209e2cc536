import '../network_config.dart';
import 'dart_define.dart';

class EnvConfig {
  final String env;
  final String domain;
  final String publicKey;

  const EnvConfig({
    required this.env,
    required this.domain,
    required this.publicKey,
  });
}

// 获取的配置信息
class Env {
  // 开发环境
  static EnvConfig _devConfig = EnvConfig(
    env: EnvName.dev,
    domain: _getHost(EnvName.dev),
    publicKey:
    "-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCAp8YJFC0kgwvYJ7Uqp0dh+e8iplKYGC2bT+lPw0Pcku0uI/vyp3casbOB8y8ZtPkcB9pP2TE/EGBW/tSbIEBqEeTPsm/+Yofn5PUkAqFAn9uRSM+4Npp4z7PiaX6fgSshqWHK0f9IXbbM97k/wmoGQgNQQGt7WpqGRYe+teXInQIDAQAB\n-----END PUBLIC KEY-----",
  );

  // 测试环境
  static EnvConfig _testConfig = EnvConfig(
    env: EnvName.test,
    domain: _getHost(EnvName.test),
    publicKey:
    "-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCAp8YJFC0kgwvYJ7Uqp0dh+e8iplKYGC2bT+lPw0Pcku0uI/vyp3casbOB8y8ZtPkcB9pP2TE/EGBW/tSbIEBqEeTPsm/+Yofn5PUkAqFAn9uRSM+4Npp4z7PiaX6fgSshqWHK0f9IXbbM97k/wmoGQgNQQGt7WpqGRYe+teXInQIDAQAB\n-----END PUBLIC KEY-----",
  );

  // stable环境
  static EnvConfig _stableConfig = EnvConfig(
    env: EnvName.stable,
    domain: _getHost(EnvName.stable),
    publicKey:
    "-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCOynL6f1PCKTxxq9aXE7QhaZFobuMlsTF/seW3JE9+9qXXzwAZCtt2mZAeTxYfSv93AHT/htQViSzVVV1kWnWpQpB8rVNCv7v5WlmdjGHZe0XTN0D+Rb9Mhd8Zy4ZU6ktE6tZDI+e4OqCjLugFejQIFfqMQf9Ha77gV67TeEL8QQIDAQAB\n-----END PUBLIC KEY-----",
  );

  // 发布环境
  static EnvConfig _releaseConfig = EnvConfig(
    env: EnvName.release,
    domain: _getHost(EnvName.release),
    publicKey:
    "-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCvRo5cCb5ScxGQsYuf/63UQFrtMgbfzN42RIEmIxZArwIsM0/O1qjE+YCo/bdThlF9KkY8lzXetwssheRjpVdZVlAsdOQ2RyX7ip+H/0R0ddMJUWdDKeBA+B3yLE1X3QZ+xQ5SdjXzepzBYswP1b5cCvOSo4IoNdSGHDig5ck2YwIDAQAB\n-----END PUBLIC KEY-----",
  );

  static EnvConfig get envConfig => _getEnvConfig();

// 根据不同环境返回对应的环境配置
  static EnvConfig _getEnvConfig() {
    switch (DartDefine.appEnv) {
      case EnvName.dev:
        return _devConfig;
      case EnvName.release:
        return _releaseConfig;
      case EnvName.test:
        return _testConfig;
      case EnvName.stable:
        return _stableConfig;
      default:
        return _devConfig;
    }
  }

  static String getEnvPre(String env) {
    String testPre = "-ceshi";
    String pre = testPre;
    switch (env) {
      case EnvName.dev:
        pre = testPre;
        break;
      case EnvName.test:
        pre = testPre;
        break;
      case EnvName.stable:
        pre = "";
        break;
      case EnvName.release:
        pre = "";
        break;
      default:
    }
    return pre;
  }

  static String getEnvPort(String env) {
    String testPort = ":444";
    String port = "";
    switch (env) {
      case EnvName.dev:
        port = testPort;
        break;
      case EnvName.test:
        port = testPort;
        break;
      case EnvName.stable:
        break;
      case EnvName.release:
        break;
      default:
    }
    return port;
  }

  static String _getHost(String env) {
    return getHost(hostType: HostType.baseHost, env: env);
  }

  static String getHost({required HostType hostType, required String env, String? newPort}) {
    String pre = getEnvPre(env);
    String port = getEnvPort(env);
    if (newPort != null) {
      port = newPort;
    }
    return "${HostType.baseHost.url}${pre}.eyyb.vip$port";
  }
}

// 声明的环境
abstract class EnvName {
  // 环境value
  static const String dev = "dev";
  static const String release = "release";
  static const String test = "test";
  static const String stable = "stable";
}
