import 'package:flutter/foundation.dart';
import 'package:lib_base/log/log.dart';

import 'env_config.dart';

class DartDefine {
  /// 获取到当前环境
  static const String appEnv =
      const String.fromEnvironment("APP_ENV", defaultValue: kReleaseMode ? EnvName.release : EnvName.test);

  // static const String appEnv = EnvName.test;

  //只显示已完成的模块
  static const String onlyShowFinishedModules =
      const String.fromEnvironment("APP_ONLY_FINISHED", defaultValue: kReleaseMode ? "true" : "false");

  //显示测试模块   默认release 返回false，   dev返回true
  static const String showTestModules =
      const String.fromEnvironment("APP_SHOW_TEST", defaultValue: kReleaseMode ? "false" : "true");

  static const String simplifyLog = const String.fromEnvironment("SIMPLIFY_LOG", defaultValue: "false");

  static void logDartDefine() {
    Logger.info(
        "=========logDartDefine==> appEnv:${appEnv}, onlyShowFinishedModules:${onlyShowFinishedModules}, showTestModules:${showTestModules}, simplifyLog:${simplifyLog}");
  }
}
