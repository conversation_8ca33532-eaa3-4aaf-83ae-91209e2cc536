import 'package:flutter/material.dart';

class ThemeConfig {
  static ThemeData currentTheme = ThemeData();
}

class ThemeData {
  //主题色
  final Color themeColor = const Color(0xFF34B6C9);
  final Color colorFFFDE9 = const Color(0xFFFFFDE9);
  final Color colorA5FFFC = const Color(0xFFA5FFFC);
  final Color colorFFD84A = const Color(0xFFFFD84A);
  final Color colorFFE55A = const Color(0xFFFFE55A);
  final Color colorB6792E = const Color(0xFFB6792E);
  final Color colorD2ECF0 = const Color(0xFFD2ECF0);
  final Color color4D2222 = const Color(0xFF4D2222);
  final Color colorFDFFEE = const Color(0xFFFDFFEE);
  final Color colorEB5737 = const Color(0xFFEB5737);
  final Color colorCED4D6 = const Color(0xFFCED4D6);
  final Color color2580FF = const Color(0xFF2580FF);
}

abstract class AppColors {
  static ThemeData _currentTheme = ThemeConfig.currentTheme;

  static Color themeColor = _currentTheme.themeColor;
  static Color textBorderColor =
      blendColors(foregroundColor: Colors.black.withOpacity(0.3), backgroundColor: themeColor);

  static Color colorFFFDE9 = _currentTheme.colorFFFDE9;
  static Color colorA5FFFC = _currentTheme.colorA5FFFC;
  static Color colorFFD84A = _currentTheme.colorFFD84A;
  static Color colorFFE55A = _currentTheme.colorFFE55A;
  static Color colorB6792E = _currentTheme.colorB6792E;
  static Color colorD2ECF0 = _currentTheme.colorD2ECF0;
  static Color color4D2222 = _currentTheme.color4D2222;
  static Color colorFDFFEE = _currentTheme.colorFDFFEE;
  static Color colorEB5737 = _currentTheme.colorEB5737;
  static Color colorCED4D6 = _currentTheme.colorCED4D6;
  static Color color2580FF = _currentTheme.color2580FF;

  //叠加颜色
  static Color blendColors({required Color foregroundColor, required Color backgroundColor}) {
    double alphaA = foregroundColor.alpha / 255.0;
    double alphaB = foregroundColor.alpha / 255.0;
    double outAlpha = alphaA + alphaB * (1 - alphaA);
    int outR = ((foregroundColor.red * alphaA + backgroundColor.red * alphaB * (1 - alphaA)) / outAlpha).round();
    int outG = ((foregroundColor.green * alphaA + backgroundColor.green * alphaB * (1 - alphaA)) / outAlpha).round();
    int outB = ((foregroundColor.blue * alphaA + backgroundColor.blue * alphaB * (1 - alphaA)) / outAlpha).round();
    return Color.fromARGB((outAlpha * 255).round(), outR, outG, outB);
  }
}
