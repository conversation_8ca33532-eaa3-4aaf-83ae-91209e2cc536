//临时的
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:lib_base/src/generated/fonts.gen.dart';

class TempThemeConfig {
  static TempThemeData currentTheme = TempThemeData();
}

class TempThemeData {
//背景色
  final Color baseBackgroundColor = const Color(0xFFF8F8F8);
  //淡黄色背景色
  final Color colorBgLittleYellow = const Color(0xffF6FFEF);
  final Color colorLittleYellow = const Color(0xffDDF5CB);
  final Color colorGreen2 = const Color(0xffE2F6DA);
  final Color colorGreenLightBg = const Color(0xffd4e9cb);
  final Color colorGreen3 = const Color(0xffAADF94);
  final Color colorDisabledGreen = const Color(0xffB3E8D1);
  final Color colorLittleYellowBg = const Color(0xffF5F9F4);
  final Color colorLittleGrown = const Color(0xffe7e2ff);
  final Color colorLittleOrange = const Color(0xffFFF3E3);
  final Color colorLittleGreen = const Color(0xffD9FAEA);
  //文字主色
  final Color colorTextPrimary1 = const Color(0xFF999999);
  final Color colorTextPrimary2 = const Color(0xFF666666);
  final Color colorTextPrimary3 = const Color(0xFF333333);
  //设计主色
  final Color colorDesignPrimary1 = const Color(0xff7DDFB5);
  final Color colorDesignPrimary2 = const Color(0xff5FD7A3);
  final Color colorDesignPrimary3 = const Color(0xFF37CE8D);
  final Color colorLightGreen = const Color(0xFFD0F3E4);

  final Color colorGreenBg1 = const Color(0xffe2f6da);
}
