import 'package:flutter/material.dart';
import 'package:lib_base/src/generated/fonts.gen.dart';

extension CustomTextStyle on TextStyle {
  TextStyle get sourceHanSansRegular {
    return copyWith(fontFamily: FontFamily.sourceHanSansCN, fontWeight: FontWeight.normal);
  }

  TextStyle get sourceHanSansMedium {
    return copyWith(fontFamily: FontFamily.sourceHanSansCN, fontWeight: FontWeight.w500);
  }

  TextStyle get sourceHanSansBold {
    return copyWith(fontFamily: FontFamily.sourceHanSansCN, fontWeight: FontWeight.bold);
  }

  TextStyle get sourceHanSansHeavy {
    return copyWith(fontFamily: FontFamily.sourceHanSansCN, fontWeight: FontWeight.w900);
  }
}
