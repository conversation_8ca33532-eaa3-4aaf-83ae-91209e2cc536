import 'package:flutter/foundation.dart';
import 'package:lib_base/config/utils/app_platforms.dart';
import 'package:lib_base/log/log.dart';

import 'env/dart_define.dart';
import 'env/env_config.dart';

// import 'env/env_config.dart';

class AppConfig {
  //某些情况下需要强制打开调试模式
  static bool debugEnabled = false;

  //是否是调试模式
  static bool get isDebugMod {
    return !isRelease || debugEnabled;
  }

  /// 是否是生产环境
  static const bool isRelease = (DartDefine.appEnv == EnvName.release) && kReleaseMode;
  // static const bool isRelease = kReleaseMode;

  ///环境信息 如:debug test release
  static EnvConfig get envConfig => Env.envConfig;

  static String appName = '';
  static String packageName = '';
  static String buildNumber = '';
  static String versionName = '';
  static String deviceId = '';

  static String brand = 'null';
  static String version = 'null';
  static String model = 'null';
  /**
   * app id 获取
   */
  static String get YYB_APPID {
    switch (AppPlatform.platform) {
      case AppPlatform.harmony:
        return _APPID_HARMONY;
      case AppPlatform.android:
        return _APPID_ANDROID;
      case AppPlatform.ios:
        return _APPID_IOS;
      default:
        return '';
    }
  }

  /**
   * 安卓 appid
   */
  static const String _APPID_ANDROID = "";
  /**
   * 鸿蒙 appId 
   * */
  static const String _APPID_HARMONY = "";
  /**
   * 苹果 appId 
   * */
  static const String _APPID_IOS = "";

  static final String APP_ENV_TYPE = "EYYB";

  static final String ossUploadAddress = "zhongtai-prod";

  /**
   * 获取BusinessId(1:英语宝;2:爱智)
   *
   * @return
   */
  static final String APP_BUSINESS_ID = '1';
}
