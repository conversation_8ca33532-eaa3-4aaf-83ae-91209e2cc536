import 'package:flutter/cupertino.dart';

class Application {
  /// app全局配置 eg:theme
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  static BuildContext getContext() {
    var context = navigatorKey.currentState?.overlay?.context;
    if (context == null) {
      throw Exception("context is null");
    }
    return context;
  }

  //当前手动设置的屏幕方向
  static Orientation? manualOrientation;
}
