import 'package:encrypt/encrypt.dart' as encrypt;

class AesUtil {
  static String encryptAes({
    required String plainText,
    required String keyStr,
    required String ivStr,
    encrypt.AESMode mode = encrypt.AESMode.cbc,
    String? padding,
  }) {
    final key = encrypt.Key.fromUtf8(keyStr);
    final iv = encrypt.IV.fromUtf8(ivStr);
    final encrypter = encrypt.Encrypter(encrypt.AES(key, mode: mode, padding: padding));

    final encrypted = encrypter.encrypt(plainText, iv: iv);

    return encrypted.base64;
  }

  static String decryptAes({
    required String encryptedStr,
    required String keyStr,
    required String ivStr,
    encrypt.AESMode mode = encrypt.AESMode.cbc,
    String? padding,
  }) {
    if (encryptedStr.isEmpty) {
      return "";
    }
    final key = encrypt.Key.fromUtf8(keyStr);
    final iv = encrypt.IV.fromUtf8(ivStr);

    final encrypter = encrypt.Encrypter(encrypt.AES(key, mode: mode, padding: padding));

    final decrypted = encrypter.decrypt64(encryptedStr, iv: iv);
    return decrypted;
  }
}
