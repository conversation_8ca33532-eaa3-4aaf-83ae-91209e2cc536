import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../application.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:lib_base/config/application.dart';
// import 'package:lib_base/src/extension/build_context_ext.dart';
// import 'package:lib_base/generated/assets.dart';
// import 'package:lib_base/src/utils/asset_utils.dart';
// import 'package:svgaplayer_flutter/player.dart';

void showExpectationToast() {
  _showToast(
    msg: '敬请期待',
    alignment: Alignment.center,
  );
}

void showToast(String msg, {Alignment? alignment}) {
  _showToast(
    msg: msg,
    alignment: alignment ?? Alignment.center,
  ); // 使用传入的alignment或默认值
}

_showToast({
  String? msg,
  WidgetBuilder? builder,
  Alignment? alignment = Alignment.center,
}) {
  SmartDialog.showToast(
    msg ?? "",
    builder: builder,
    displayType: SmartToastType.last,
    alignment: alignment ?? Alignment.center, // 使用传入的alignment或默认值
  );
}

showLoading({String? msg, bool backDismiss = true, bool clickMaskDismiss = false, SmartDialogController? controller}) {
  _showLoading(
    msg: msg,
    backDismiss: backDismiss,
    clickMaskDismiss: clickMaskDismiss,
    controller: controller,
  );
}

showLoadingWithCustomBuilder(Widget widget) {
  _showLoading(
    builder: (BuildContext context) => widget,
  );
}

showWebViewLoading({bool webLoading = true}) {
  if (webLoading) {
    showSmartDialog(
      Stack(
        children: [
          // Image.asset(
          //   AssetsUtils.wrapAsset(ImageAssets.imagesH5LoadingBg),
          //   width: double.infinity,
          //   height: double.infinity,
          //   fit: BoxFit.fill,
          // ),
          // SVGASimpleImage(
          //   assetsName: AssetsUtils.wrapAsset(ImageAssets.imagesH5Loading),
          // ),
          // SvgPicture.asset(
          //   AssetsUtils.wrapAsset(ImageAssets.imagesH5Loading),
          // )
        ],
      ),
      tag: "webViewLoadingDialog",
      animationType: SmartAnimationType.fade,
      backDismiss: true,
      clickMaskDismiss: false,
    );
  } else {
    showLoading();
  }
}

dismissWebViewLoading({bool webLoading = true}) {
  if (webLoading) {
    dismissDialog(tag: "webViewLoadingDialog");
  } else {
    dismissLoading();
  }
}

_showLoading({
  String? msg,
  WidgetBuilder? builder,
  bool? backDismiss,
  bool? clickMaskDismiss,
  SmartDialogController? controller,
  Duration? displayTime,
}) {
  SmartDialog.showLoading(
      msg: msg ?? '加载中',
      backDismiss: backDismiss,
      clickMaskDismiss: clickMaskDismiss,
      builder: builder,
      displayTime: displayTime,
      controller: controller);
}

dismissLoading() {
  SmartDialog.dismiss(status: SmartStatus.loading);
}

dismissDialog<T>({T? result, SmartStatus? status, String? tag}) {
  SmartDialog.dismiss(
    status: status ?? SmartStatus.custom,
    tag: tag,
    result: result,
  );
}

Future<T?> showSmartDialog<T>(
  Widget widget, {
  bool backDismiss = true,
  bool clickMaskDismiss = true,
  String? tag,
  Alignment? alignment,
  Color? maskColor,
  bool keepSingle = true,
  bool? debounce,
  SmartAnimationType? animationType,
  Rect? ignoreArea,
  bool? usePenetrate,
  bool? permanent,
  Widget? maskWidget,
  VoidCallback? onDismiss,
}) async {
  return await SmartDialog.show<T>(
    tag: tag,
    builder: (BuildContext context) => widget,
    backDismiss: backDismiss,
    clickMaskDismiss: clickMaskDismiss,
    alignment: alignment ?? Alignment.center,
    keepSingle: keepSingle,
    maskColor: maskColor,
    animationType: animationType,
    ignoreArea: ignoreArea,
    debounce: debounce,
    usePenetrate: usePenetrate,
    permanent: permanent,
    maskWidget: maskWidget,
    onDismiss: onDismiss,
  );
}

dismissAttachDialog<T>({T? result, SmartStatus? status, String? tag}) {
  return dismissDialog(status: SmartStatus.attach, result: result, tag: tag);
}

Future<T?> showSmartAttachDialog<T>(
  Widget widget,
  BuildContext targetContext, {
  bool backDismiss = true,
  bool clickMaskDismiss = true,
  Color? maskColor,
  String? tag,
  Alignment? alignment,
  Rect? maskIgnoreArea,
  bool? bindPage,
  BuildContext? bindWidget,
  VoidCallback? onMask,
  bool keepSingle = true,
  bool? usePenetrate,
}) async {
  return await SmartDialog.showAttach<T>(
      targetContext: targetContext,
      builder: (BuildContext c) {
        return widget;
      },
      tag: tag,
      onMask: onMask,
      backDismiss: backDismiss,
      clickMaskDismiss: clickMaskDismiss,
      alignment: alignment,
      maskColor: maskColor,
      maskIgnoreArea: maskIgnoreArea,
      bindPage: bindPage,
      bindWidget: bindWidget,
      keepSingle: keepSingle,
      usePenetrate: usePenetrate);
}
