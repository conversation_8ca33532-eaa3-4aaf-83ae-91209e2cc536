import 'dart:io';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

enum AppPlatform {
  harmony('ohos'),
  ios('ios'),
  android('android'),
  unknown('unknown');

  final String name;
  const AppPlatform(this.name);

  static bool get isIos {
    if (kIsWeb) {
      return false;
    } else {
      return Platform.isIOS;
    }
  }

  static bool get isHarmony {
    if (kIsWeb) {
      return false;
    } else {
      return Platform.operatingSystem.toLowerCase() == 'ohos';
    }
  }

  static bool get isAndroid {
    if (kIsWeb) {
      return false;
    } else {
      return Platform.isAndroid;
    }
  }

  static bool get isMobileOS {
    if (kIsWeb) {
      return false;
    } else {
      return Platform.isIOS || Platform.isAndroid;
    }
  }

  static bool get isDesktopOS {
    if (kIsWeb) {
      return false;
    } else {
      return Platform.isLinux || Platform.isFuchsia || Platform.isWindows || Platform.isMacOS;
    }
  }

  static bool isTabletByInches(BuildContext context) {
    final MediaQueryData data = MediaQuery.of(context);
    final double width = data.size.width / data.devicePixelRatio;
    final double height = data.size.height / data.devicePixelRatio;
    final double diagonalInches = sqrt(pow(width, 2) + pow(height, 2)) / data.devicePixelRatio;
    return diagonalInches >= 7.0;
  }

  static AppPlatform get platform {
    if (isHarmony) {
      return AppPlatform.harmony;
    } else if (isIos) {
      return AppPlatform.ios;
    } else if (isAndroid) {
      return AppPlatform.android;
    } else {
      return AppPlatform.unknown;
    }
  }
}
