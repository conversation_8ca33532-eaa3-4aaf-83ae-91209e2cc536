import 'dart:convert';
import 'package:lib_base/config/storage_manager.dart';
import 'package:lib_base/config/utils/md5_util.dart';
import 'package:lib_base/config/utils/app_platforms.dart';
import 'package:lib_base/log/log.dart';
import '../env/env_config.dart';

class HttpUtil {
  static String phonePlatform() {
    if (AppPlatform.isAndroid) {
      return 'Android';
    } else if (AppPlatform.isIos) {
      return 'Ios';
    } else if (AppPlatform.isHarmony) {
      return 'Ohos';
    }
    return 'Unknown';
  }
}
