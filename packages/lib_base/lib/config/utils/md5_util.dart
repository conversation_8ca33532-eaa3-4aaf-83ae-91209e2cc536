import 'dart:convert';
import 'package:crypto/crypto.dart';

class Md5Util {
  static String encrypt(String plaintext) {
    var hexDigits = [
      '0',
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      'A',
      'B',
      'C',
      'D',
      'E',
      'F'
    ];

    var md = md5.convert(utf8.encode(plaintext)).bytes;

    int j = md.length;
    List<String> str = List.filled(j * 2, "", growable: false);
    int k = 0;
    for (int i = 0; i < j; i++) {
      var byte0 = md[i];
      str[k++] = hexDigits[byte0 >>> 4 & 0xf];
      str[k++] = hexDigits[byte0 & 0xf];
    }
    return str.join();
  }
}
