import 'dart:convert';

import 'package:lib_base/log/log.dart';
import 'package:shared_preferences/shared_preferences.dart';

/**
 * 注意， 这个类用来对sharepreface 的字符串存储进行加解码处理， 以应对政策要求的不能明文存储用户信息的要求
 */
class CustomSharedPreferences {
  // / app全局配置 eg:theme
  static late SharedPreferences _sharedPreferences;

  static CustomSharedPreferences? _instance;

  CustomSharedPreferences._();

  /// 必备数据的初始化操作
  ///
  /// 由于是同步操作会导致阻塞,所以应尽量减少存储容量
  static Future<CustomSharedPreferences> initSp() async {
    if (_instance == null) {
      // async 异步操作
      // sync 同步操作
      SharedPreferences.setPrefix("");
      _sharedPreferences = await SharedPreferences.getInstance();
      _instance = CustomSharedPreferences._();
    }
    return _instance!;
  }

  Set<String> keys() => _sharedPreferences.getKeys();

  /// Reads a value from persistent storage, throwing an exception if it's not a
  /// bool.
  bool? getBool(String key) => _sharedPreferences.getBool(key);

  /// Reads a value from persistent storage, throwing an exception if it's not
  /// an int.
  int? getInt(String key) => _sharedPreferences.getInt(key);

  /// Reads a value from persistent storage, throwing an exception if it's not a
  /// double.
  double? getDouble(String key) {
    return _sharedPreferences.getDouble(key);
  }

  /// Returns true if persistent storage the contains the given [key].
  bool containsKey(String key) => _sharedPreferences.containsKey(key);

  /// Saves a boolean [value] to persistent storage in the background.
  Future<bool> setBool(String key, bool value) {
    return _sharedPreferences.setBool(key, value);
  }

  /// Saves an integer [value] to persistent storage in the background.
  Future<bool> setInt(String key, int value) {
    return _sharedPreferences.setInt(key, value);
  }

  /// Saves a double [value] to persistent storage in the background.
  ///
  /// Android doesn't support storing doubles, so it will be stored as a float.
  Future<bool> setDouble(String key, double value) {
    return _sharedPreferences.setDouble(key, value);
  }

  /// Removes an entry from persistent storage.
  Future<bool> remove(String key) {
    return _sharedPreferences.remove(key);
  }

  Future<bool> setJsonList<T>(String key, List<T> value) {
    List<String> strList = [];
    for (var element in value) {
      strList.add(jsonEncode(element));
    }
    return setStringList(key, strList);
  }

  List<Map<String, dynamic>>? getJsonList(String key) {
    List<String>? value = getStringList(key);
    if (value != null) {
      List<Map<String, dynamic>> result = [];
      for (var element in value) {
        result.add(jsonDecode(element));
      }
      return result;
    }
    return null;
  }
  //
  // String? getWebViewValue(String key) {
  //   return getString("${CustomSharedPreferences.webviewPre}_$key");
  // }
  //
  // Future<bool> removeWebViewValue(String key) {
  //   return remove("${CustomSharedPreferences.webviewPre}_$key");
  // }
  //
  // Future<bool> setWebViewValue(String key, String value) {
  //   return setString("${CustomSharedPreferences.webviewPre}_$key", value);
  // }

  /**  需要加码 开始 **/

  //base64加密后， 加上这个前缀， 用于区分未加密版本和已加密的v1版本
  static const encodePrefV1 = "==========v1";

  String? _decodeStorageStr(String? value) {
    if (value?.isNotEmpty ?? false) {
      if (value!.startsWith(encodePrefV1)) {
        return utf8.decode(base64Decode(value.substring(encodePrefV1.length)));
      } else {
        return value;
      }
    } else {
      return value;
    }
  }

  String? _encodeStorageStr(String? value) {
    if (value?.isNotEmpty ?? false) {
      return "${encodePrefV1}${base64Encode(utf8.encode(value!))}";
    } else {
      return value;
    }
  }

  /// Reads a value from persistent storage, throwing an exception if it's not a
  /// String.
  String? getString(String key) {
    String? value = _sharedPreferences.getString(key);
    return _decodeStorageStr(value);
  }

  /// Reads a set of string values from persistent storage, throwing an
  /// exception if it's not a string set.
  List<String>? getStringList(String key) {
    List<String>? values = _sharedPreferences.getStringList(key);
    if (values?.isNotEmpty ?? false) {
      List<String> result = [];
      for (String v in values!) {
        result.add(_decodeStorageStr(v)!);
      }
      return result;
    } else {
      return values;
    }
  }

  /// Saves a string [value] to persistent storage in the background.
  ///
  /// Note: Due to limitations in Android's SharedPreferences,
  /// values cannot start with any one of the following:
  ///
  /// - 'VGhpcyBpcyB0aGUgcHJlZml4IGZvciBhIGxpc3Qu'
  /// - 'VGhpcyBpcyB0aGUgcHJlZml4IGZvciBCaWdJbnRlZ2Vy'
  /// - 'VGhpcyBpcyB0aGUgcHJlZml4IGZvciBEb3VibGUu'
  Future<bool> setString(String key, String value) {
    return _sharedPreferences.setString(key, _encodeStorageStr(value)!);
  }

  /// Saves a list of strings [value] to persistent storage in the background.
  Future<bool> setStringList(String key, List<String> value) {
    List<String> encodes = [];
    for (String v in value) {
      encodes.add(_encodeStorageStr(v)!);
    }
    return _sharedPreferences.setStringList(key, encodes);
  }

  static String webviewPre = "webview_pref";

  Future<bool> setJson(String key, dynamic value) {
    String jsonStr = jsonEncode(value);
    return setString(key, _encodeStorageStr(jsonStr)!);
  }

  Map<String, dynamic>? getJson(String key) {
    String? value = getString(key);
    if (value != null) {
      value = _decodeStorageStr(value)!;
      Map<String, dynamic>? json = jsonDecode(value);
      return json;
    }
    return null;
  }
}
