import 'package:localstorage/localstorage.dart';

extension LocalStorageExtention on LocalStorage {
  static Map<String, dynamic> _setItemAsyncMap = {};

  static bool _isRunning = false;

  void setItemAsync(
    String key,
    value,
  ) {
    _setItemAsyncMap[key] = value;
    if (!_isRunning) {
      _doSave();
    }
  }

  Future _doSave() async {
    _isRunning = true;
    //TODO: 临时注释掉
    var key = _setItemAsyncMap.keys.first;
    // setItem(key, _setItemAsyncMap[key]).then((v) {
    //   _setItemAsyncMap.remove(key);
    //   if (_setItemAsyncMap.isNotEmpty) {
    //     _doSave();
    //   } else {
    //     _isRunning = false;
    //   }
    // });
  }

  dynamic getItemAsync(String key) {
    if (_setItemAsyncMap.containsKey(key)) {
      return _setItemAsyncMap[key];
    }
    return getItem(key);
  }
}
