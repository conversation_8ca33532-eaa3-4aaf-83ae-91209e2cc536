import 'dart:convert';
import 'dart:typed_data';

import 'package:encrypt/encrypt.dart' as Enc;
import 'package:encrypt/encrypt.dart';
import 'package:flutter/services.dart' show Uint8List;

import 'package:pointycastle/asymmetric/api.dart';

class RsaUtil {
  // static const String pubKeySimple =
  //     "-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChI43895+UBo7nNL99IiRWuI7TldJByKno4OtUbXD3gk/NWo2/lUPZkhPnWKiZ6+zfB9FIO8FYa9UwaZ99+wuhb/che5D+bXfXhOhWNKA3RzvPnIRneu5V08vtiLaRKpMGQ6CQ/NeWk39xBWDJt1owfpQ+qr6bfgiKzLHEWescQwIDAQAB\n-----<PERSON><PERSON> PUBLIC KEY-----";

  static RSAPublicKey getPublicKey(String publicKeyStr) {
    return Enc.RSAKeyParser().parse(publicKeyStr) as RSAPublicKey;
  }

  static RSAPrivateKey getPrivateKey(String privateKeyStr) {
    return Enc.RSAKeyParser().parse(privateKeyStr) as RSAPrivateKey;
  }

  static Future<String> encodeStringWithPubKey(
      String pubKey, String content) async {
    var publicKey = getPublicKey(pubKey);
    final encrypter = Encrypter(RSA(publicKey: publicKey));
    List<int> sourceBytes = utf8.encode(content);
    int inputLen = sourceBytes.length;
    int maxLen = 117;
    List<int> totalBytes = [];
    for (var i = 0; i < inputLen; i += maxLen) {
      int endLen = inputLen - i;
      List<int> item;
      if (endLen > maxLen) {
        item = sourceBytes.sublist(i, i + maxLen);
      } else {
        item = sourceBytes.sublist(i, i + endLen);
      }
      totalBytes.addAll(encrypter.encryptBytes(item).bytes);
    }
    return base64.encode(totalBytes);
//       return await encrypter.encrypt(content).base64.toUpperCase();
  }

  static Future<String> decodeStringWithPubKey(
      String pubKey, String content) async {
    var publicKey = getPublicKey(pubKey);
    final encrypter = Encrypter(RSA(publicKey: publicKey));
    Uint8List sourceBytes = base64.decode(content);
    int inputLen = sourceBytes.length;
    int maxLen = 128;
    List<int> totalBytes = [];
    for (var i = 0; i < inputLen; i += maxLen) {
      int endLen = inputLen - i;
      Uint8List item;
      if (endLen > maxLen) {
        item = sourceBytes.sublist(i, i + maxLen);
      } else {
        item = sourceBytes.sublist(i, i + endLen);
      }
      totalBytes.addAll(encrypter.decryptBytes(Enc.Encrypted(item)));
    }
    return utf8.decode(totalBytes);
//        return await encrypter.decrypt(Encrypted.fromBase64(content));
  }
}
