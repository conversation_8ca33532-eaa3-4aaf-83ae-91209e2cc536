import 'package:flutter/material.dart';
import 'package:lib_base/config/theme/theme_font.dart';

class BorderedText extends StatelessWidget {
  final String text;
  final Color borderColor;
  final double strokeWidth;
  final TextStyle textStyle;
  const BorderedText(
      {super.key,
      required this.text,
      this.borderColor = Colors.black,
      this.strokeWidth = 6,
      this.textStyle = const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)});
  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Text(text,
            style: textStyle.copyWith(
                fontWeight: FontWeight.w900,
                foreground: Paint()
                  ..style = PaintingStyle.stroke
                  ..strokeWidth = strokeWidth
                  ..color = borderColor)),
        Text(
          text,
          style: textStyle,
        ),
      ],
    );
  }
}
