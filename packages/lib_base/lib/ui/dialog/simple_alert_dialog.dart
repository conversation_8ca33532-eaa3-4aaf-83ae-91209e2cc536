import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/config/theme/temp_theme_config.dart';
import 'package:lib_base/config/utils/ui_util.dart';


/// 没有描述的assert 弹框
class SimpleAlertDialog extends StatelessWidget {
  final String title;
  final String? desc;
  final TextStyle? titleStyle;
  final Color? titleBg;
  final TextStyle? descStyle;
  final Color? titleColor;
  final Color? descColor;
  final String? confirmText;
  final String? cancelText;
  final GestureTapCallback? cancel;
  final GestureTapCallback? confirm;
  final bool singleButton;

  const SimpleAlertDialog(
      {super.key,
      required this.title,
      this.cancel,
      this.confirm,
      this.cancelText,
      this.confirmText,
      this.desc,
      this.titleColor,
      this.descColor,
      this.singleButton = false,
      this.descStyle,
      this.titleStyle,
      this.titleBg});

  static Future showDialog(
      {required String title,
      GestureTapCallback? cancel,
      String? desc,
      GestureTapCallback? confirm,
      String? confirmText,
      String? cancelText,
      Color? titleColor,
      Color? descColor,
      bool backDismiss = false}) {
    return showSmartDialog(
        SimpleAlertDialog(
          title: title,
          desc: desc,
          cancel: cancel,
          confirm: confirm,
          confirmText: confirmText,
          cancelText: cancelText,
          titleColor: titleColor,
          descColor: descColor,
        ),
        backDismiss: backDismiss,
        clickMaskDismiss: backDismiss);
  }

  @override
  Widget build(BuildContext context) {
    double defaultHorizontalPadding = 30.r;
    List<Widget> children = [
      _SimpleButton(
        padding: EdgeInsets.symmetric(
            horizontal: cancelText == null ? defaultHorizontalPadding : 20.r,
            vertical: 5.r),
        text: cancelText ?? '取消',
        radius: 20.r,
        boarderColor: TempThemeConfig.currentTheme.colorDesignPrimary3,
        textStyle: TextStyle(
            fontSize: 18.sp,
            color: TempThemeConfig.currentTheme.colorTextPrimary1,
            overflow: TextOverflow.ellipsis),
        onTap: cancel ?? () => dismissDialog(),
      ),
      _SimpleButton(
        padding: EdgeInsets.symmetric(
            horizontal: confirmText == null ? defaultHorizontalPadding : 20.r,
            vertical: 5.r),
        text: confirmText ?? "确认",
        radius: 20.r,
        boarderColor: TempThemeConfig.currentTheme.colorDesignPrimary3,
        textStyle: TextStyle(
            fontSize: 18.sp,
            color: Colors.white,
            overflow: TextOverflow.ellipsis),
        bg: TempThemeConfig.currentTheme.colorDesignPrimary3,
        onTap: confirm,
      )
    ];
    if (singleButton) {
      children.removeAt(0);
    }
    return Container(
      width: 1.sw - 40.r,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          color: Colors.white),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(10.r),
                      topRight: Radius.circular(10.r)),
                  color: titleBg,
                ),
                margin: EdgeInsets.only(bottom: 15.r),
                alignment: Alignment.center,
                width: double.infinity,
                padding: EdgeInsets.only(top: 30.r, bottom: 10.r),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 10.r),
                  child: Text(
                    title,
                    style: titleStyle ??
                        TextStyle(
                            fontSize: 18.sp,
                            color: titleColor ??
                                TempThemeConfig.currentTheme.colorTextPrimary3),
                  ),
                ),
              ),
              desc != null
                  ? Center(
                      child: Padding(
                        padding: EdgeInsets.only(
                          bottom: 19.r,
                          left: 20.r,
                          right: 20.r,
                        ),
                        child: Text(
                          desc!,
                          style: descStyle ??
                              TextStyle(
                                  fontSize: 17.sp,
                                  color: descColor ??
                                      TempThemeConfig
                                          .currentTheme.colorTextPrimary2),
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),
            ],
          ),
          Padding(
            padding: EdgeInsets.only(bottom: 25.r),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: children,
            ),
          )
        ],
      ),
    );
  }
}


class _SimpleButton extends StatelessWidget {

  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? bg;
  final double? radius;
  final String? text;
  final TextStyle? textStyle;
  final Color? textColor;
  final Color? boarderColor;
  final Widget? textWidget;
  final Gradient? bgColors;
  final GestureTapCallback? onTap;

  const _SimpleButton(
      {super.key,
        this.width,
        this.height,
        this.padding,
        this.bg,
        this.radius,
        this.text,
        this.textStyle,
        this.textWidget,
        this.margin,
        this.textColor,
        this.boarderColor,
        this.bgColors,
        this.onTap});



  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        alignment: Alignment.center,
        width: width,
        height: height,
        padding: padding,
        margin: margin,
        decoration: BoxDecoration(
            color: bgColors == null ? bg : null,
            borderRadius:
            radius != null ? BorderRadius.circular(radius!) : null,
            border:
            boarderColor != null ? Border.all(color: boarderColor!) : null,
            gradient: (bgColors != null) ? bgColors : null),
        child: textWidget ??
            (text != null
                ? Text(
              text!,
              style: textStyle ??
                  TextStyle(
                      fontSize: 14.sp,
                      color: textColor ??
                          TempThemeConfig.currentTheme.colorTextPrimary3),
            )
                : null),
      ),
    );
  }

}