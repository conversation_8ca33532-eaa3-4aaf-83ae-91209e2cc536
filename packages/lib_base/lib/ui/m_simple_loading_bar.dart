import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class MSimpleLoadingBar extends StatefulWidget {
  final size;
  // 旋转速度，默认：1转/秒
  final double speed;
  const MSimpleLoadingBar({
    super.key,
    this.size = 36.0,
    this.speed = 1.0,
  })  : assert(speed > 0),
        assert(size > 0);

  @override
  State<MSimpleLoadingBar> createState() => _MSimpleLoadingBarState();
}

class _MSimpleLoadingBarState extends State<MSimpleLoadingBar>
    with SingleTickerProviderStateMixin {
  late Animation<double> animation;
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    int milliseconds = 1200;
    _controller = AnimationController(
        duration: Duration(milliseconds: milliseconds), vsync: this)
      ..repeat(reverse: false);
    animation = Tween<double>(begin: 0, end: 1).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.linear,
    ));

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _controller.repeat();
    });
  }

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: animation,
      alignment: Alignment.center,
      child: Container(
        width: widget.size,
        height: widget.size,
        alignment: Alignment.center,
        child:Wrap(
          children: [
            Container(
              width: 150,
              height: 150,
              decoration: BoxDecoration(
                  color: Colors.amber, shape: BoxShape.circle),
            ),
            Container(
              width: 150,
              height: 150,
              decoration: BoxDecoration(
                  color: Colors.green, shape: BoxShape.circle),
            ),
            Container(
              width: 150,
              height: 150,
              decoration: BoxDecoration(
                  color: Colors.indigo, shape: BoxShape.circle),
            ),
            Container(
              width: 150,
              height: 150,
              decoration: BoxDecoration(
                  color: Colors.red, shape: BoxShape.circle),
            )
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    if (_controller.status != AnimationStatus.completed &&
        _controller.status != AnimationStatus.dismissed) {
      _controller.stop();
    }

    _controller.dispose();
    super.dispose();
  }
}
