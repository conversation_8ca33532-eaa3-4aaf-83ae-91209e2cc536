import 'package:flutter/material.dart';

extension CustomButtonExtension on TextButton {
  static ButtonStyle get customStyle => TextButton.styleFrom(
        foregroundColor: Colors.white, backgroundColor: Colors.black87, // 根据你的样式调整颜色
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(25), // 类似你的“椭圆”边角
        ),
        padding: EdgeInsets.symmetric(horizontal: 40, vertical: 12), // 文字颜色
        textStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
      );

  static Widget getGetStartedButton({required VoidCallback onPressed, required String text}) {
    return TextButton(
      onPressed: onPressed,
      style: customStyle,
      child: Text(text),
    );
  }
}
