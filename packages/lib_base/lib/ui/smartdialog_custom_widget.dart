import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/ui/m_simple_loading_bar.dart';

class LoadingWidget extends StatelessWidget {
  const LoadingWidget({super.key, required this.msg, this.bg});

  ///loading msg
  final String msg;

  final Color? bg;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 20),
      decoration: BoxDecoration(
        color: bg ?? Colors.white,
        borderRadius: BorderRadius.circular(15.r),
      ),
      child: Column(mainAxisSize: MainAxisSize.min, children: [
        // const CircularProgressIndicator(
        //   strokeWidth: 3,
        //   valueColor: AlwaysStoppedAnimation(Colors.blue),
        // ),
        MSimpleLoadingBar(),
        //msg
        Container(
          margin: const EdgeInsets.only(top: 20),
          child: Text(msg, style: const TextStyle(color: Colors.black)),
        ),
      ]),
    );
  }
}

class ToastWidget extends StatelessWidget {
  const ToastWidget({super.key, required this.msg});

  ///toast msg
  final String msg;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        margin:   EdgeInsets.symmetric(horizontal: 30.r, vertical: 200.r),
        padding:   EdgeInsets.symmetric(horizontal: 25.r, vertical: 10.r),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: const [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 10,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Text(msg, style:   TextStyle(fontSize: 14.sp),overflow: TextOverflow.visible,),
      ),
    );
  }
}
