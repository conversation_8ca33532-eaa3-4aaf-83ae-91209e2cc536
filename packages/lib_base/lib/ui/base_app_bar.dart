// import 'package:flutter/material.dart';
// import 'package:lib_base/src/generated/assets.gen.dart';

// class BaseAppBar extends StatefulWidget implements PreferredSizeWidget {
//   Widget? leading;
//   String? title;
//   List<Widget>? actions;
//   BaseAppBar({Key? key, this.leading, this.title, this.actions});

//   @override
//   State<StatefulWidget> createState() => _BaseAppBarState();

//   @override
//   Size get preferredSize => const Size(double.infinity, kToolbarHeight);
// }

// class _BaseAppBarState extends State<BaseAppBar> {
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       color: Colors.white,
//       child: Column(
//         children: [
//           const Spacer(),
//           Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 35),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.start,
//                 children: [
//                   widget.leading ?? _leading(),
//                   Text(
//                     widget.title ?? '',
//                     style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
//                   ),
//                   const Spacer(),
//                   Row(
//                     children: widget.actions ?? [],
//                   )
//                 ],
//               ))
//         ],
//       ),
//     );
//   }

//   Widget _leading() {
//     return Container(
//         margin: const EdgeInsets.only(right: 20),
//         width: 45,
//         height: 45,
//         decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(15),
//           color: Colors.transparent,
//           boxShadow: [
//             BoxShadow(color: Colors.grey[300] ?? Colors.black, offset: const Offset(0, 0), blurRadius: 10),
//           ],
//         ),
//         child: //SizedBox()
//             ElevatedButton(
//                 style: ButtonStyle(
//                     foregroundColor: WidgetStateProperty.resolveWith((states) => Colors.white.withValues(alpha: 0.9)),
//                     backgroundColor: WidgetStateProperty.resolveWith((states) {
//                       if (states.isNotEmpty && states.first == WidgetState.pressed) {
//                         return Colors.white.withValues(alpha: 0.9);
//                       }
//                       return Colors.white.withValues(alpha: 1);
//                     }),
//                     shape: WidgetStatePropertyAll(RoundedSuperellipseBorder(borderRadius: BorderRadius.circular(15))),
//                     padding: const WidgetStatePropertyAll(EdgeInsets.zero),
//                     fixedSize: WidgetStateProperty.all(const Size(45, 45)),
//                     shadowColor: WidgetStateProperty.all(Colors.black.withValues(alpha: 1)),
//                     overlayColor: WidgetStateProperty.all(Colors.transparent),
//                     elevation: WidgetStateProperty.all(0)),
//                 onPressed: () {
//                   final navigatorState = Navigator.of(context);
//                   if (navigatorState.canPop()) {
//                     navigatorState.pop();
//                   }
//                 },
//                 child: Assets.images.light.backIcon.svg() //.images.light.backIconDark.svg(),
//                 ));
//   }
// }
