name: lib_base
description: "基础模块."
version: 0.0.1

environment:
  sdk: '>=2.19.6 <3.0.0'

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: 1.0.2
  go_router:
    git:
      url: "https://gitcode.com/openharmony-sig/flutter_packages.git"
      path: "packages/go_router"
  flutter_screenutil:  5.9.0 
  dio: 5.4.2+1
  permission_handler:
   git:
     url: https://gitcode.com/openharmony-sig/flutter_permission_handler.git
     ref: master
     path: permission_handler
  
  localstorage: 4.0.1+2
  shared_preferences:
    git:
      url: "https://gitcode.com/openharmony-sig/flutter_packages.git"
      path: "packages/shared_preferences/shared_preferences"
  encrypt:  5.0.1
 
  # easy_refresh: 3.4.0
  flutter_smart_dialog: 4.9.6
  # cached_network_image: 3.3.0
  date_format: 2.0.7
  # uuid:  4.5.1
  # package_info_plus:
  #   git:
  #     url: "https://gitcode.com/openharmony-sig/flutter_plus_plugins.git"
  #     path: "packages/package_info_plus/package_info_plus"
  # # path_provider:
  # #   git:
  # #     url: "https://gitcode.com/openharmony-sig/flutter_packages.git"
  # #     path: "packages/path_provider/path_provider"
  # url_launcher:
  #   git:
  #     url: "https://gitcode.com/openharmony-sig/flutter_packages.git"
  #     path: "packages/url_launcher/url_launcher"
  # archive: 3.6.1
  # flutter_downloader: 1.10.3
  # video_player:
  #   git:
  #     url: "https://gitcode.com/openharmony-tpc/flutter_packages.git"
  #     path: "packages/video_player/video_player"
  #     ref: br_video_player-v2.9.2_ohos
  # flutter_sound:
  #   git:
  #     url: "https://gitcode.com/openharmony-sig/flutter_sound.git"
  #     path: "flutter_sound"
  # svgaplayer_flutter: 2.2.0
  # ffmpeg_kit_flutter: 6.0.3
  # flutter_oss_aliyun: 6.4.2
  # dh_picker:
  #   git:
  #     url: "http://*************:10080/flutter/flutter_plugins.git"
  #     path: dh_picker
  # dh_picker:
  #   path: ../../../../../clone/flutter_plugins/dh_picker
  # connectivity_plus:
  #   git:
  #     url: "https://gitcode.com/openharmony-sig/flutter_plus_plugins.git"
  #     path: "packages/connectivity_plus/connectivity_plus"
  # mobile_scanner:
  #   git:
  #     url: "https://gitcode.com/openharmony-sig/fluttertpc_mobile_scanner"
  # image_picker:
  #   git:
  #     url: "https://gitcode.com/openharmony-sig/flutter_packages.git"
  #     path: "packages/image_picker/image_picker"
  # dotted_border:  2.1.0
  # dotted_line:  3.2.3
  # flutter_widget_from_html_core: 0.14.7
  # card_swiper: 3.0.1
  # just_audio: 
  #   git:
  #     url: "https://gitcode.com/openharmony-sig/fluttertpc_just_audio"
  #     path: "just_audio"
  # just_audio_ohos: 
  #   git:
  #     url: "https://gitcode.com/openharmony-sig/fluttertpc_just_audio"
  #     path: "just_audio/ohos"
  #     ref: br_just_audio-v0.9.41_ohos
  # calendar_date_picker2: 1.1.9
  # fluwx: 5.5.4
    #智能体
  # ai_intelligent_flutter:
  #   git:
  #     url: "http://*************:10080/flutter/ai_intelligent_flutter.git"
  # ai_intelligent_flutter:
  #   path: ../../../../android/YYBAndroid/flutter/ai_intelligent_flutter
  flutter_svg:
  # audioplayers:
  #   git:
  #     url: https://gitcode.com/openharmony-sig/flutter_audioplayers.git
  #     ref: master
  #     path: packages/audioplayers
  # scrollview_observer: 1.21.0
  flutter_riverpod:
  riverpod_annotation:
  json_annotation:
dev_dependencies:
  flutter_test:
    sdk: flutter

  retrofit_generator:
  json_serializable:
  build_runner: 2.4.11
  # the code generator
  riverpod_generator:
  flutter_gen_runner: 5.8.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: sourceHanSansCN
      fonts:
        - asset: assets/fonts/sourceHanSansCN-VF.otf
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages

assets:
    - assets/images/

flutter_gen:
  output: lib/src/generated/ # Optional (default: lib/gen/)
  line_length: 80 # Optional (default: 80)
  assets:
    outputs:
       package_parameter_enabled: true
       class_name: Assets # <- Add this line.
       directory_path_enabled: true
    exclude:
      - assets/html/**

 # Optional
  integrations:
    flutter_svg: true
    rive: true
    lottie: true