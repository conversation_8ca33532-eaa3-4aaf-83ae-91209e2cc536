class DartCustomTheme extends CustomTheme {
  @override
  final ThemeMode themeMode = ThemeMode.dark;

  @override
  final Color colorDesignPrimary3 = Colors.white;
  @override
  final Color baseBackgroundColor = const Color(0xff000000);

  @override
  ThemeData getTheme() {
    return ThemeData(
        brightness: Brightness.dark,
        useMaterial3: true,
        appBarTheme: AppBarTheme(backgroundColor: baseBackgroundColor));
  }
}

abstract class ThemeConfig {
  static late CustomTheme _customThem;

  static CustomTheme get currentTheme => _customThem;

  static final Map<ThemeMode, CustomTheme> _themeMap = {
    ThemeMode.light: CustomTheme(),
    ThemeMode.dark: DartCustomTheme(),
  };

  static CustomTheme initTheme(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.dark:
        _customThem = _themeMap[ThemeMode.dark]!;
        break;
      default:
        _customThem = _themeMap[ThemeMode.light]!;
    }
    return _customThem;
  }
}